using AcademicPerformance.Models.MongoDocuments;

namespace AcademicPerformance.Interfaces
{
    /// <summary>
    /// Submission data access interface - MongoDB operations
    /// </summary>
    public interface ISubmissionStore
    {
        #region Submission CRUD Operations

        /// <summary>
        /// Akademisyen ve form ID'sine göre submission getir
        /// </summary>
        Task<AcademicSubmissionDocument?> GetSubmissionByFormIdAsync(string academicianUserId, string formId);

        /// <summary>
        /// Submission'ı ID ile getir
        /// </summary>
        Task<AcademicSubmissionDocument?> GetSubmissionByIdAsync(string submissionId);

        /// <summary>
        /// Akademisyenin tüm submission'larını getir
        /// </summary>
        Task<List<AcademicSubmissionDocument>> GetSubmissionsByAcademicianAsync(string academicianUserId);

        /// <summary>
        /// Yeni submission oluştur
        /// </summary>
        Task<AcademicSubmissionDocument> CreateSubmissionAsync(AcademicSubmissionDocument document);

        /// <summary>
        /// Submission'ı güncelle
        /// </summary>
        Task<bool> UpdateSubmissionAsync(AcademicSubmissionDocument document);

        /// <summary>
        /// Submission'ı sil
        /// </summary>
        Task<bool> DeleteSubmissionAsync(string submissionId);

        /// <summary>
        /// Submission status'unu güncelle
        /// </summary>
        Task<bool> UpdateSubmissionStatusAsync(string submissionId, string status, DateTime? submittedAt = null);

        #endregion

        #region Criterion Data Operations

        /// <summary>
        /// Kriter data'sını upsert et (varsa güncelle, yoksa ekle)
        /// </summary>
        Task<bool> UpsertCriterionDataAsync(string submissionId, string criterionLinkId, SubmissionCriterionData criterionData);

        /// <summary>
        /// Belirli bir kriter data entry'sini güncelle
        /// </summary>
        Task<bool> UpdateCriterionDataEntryAsync(string submissionId, string criterionLinkId, string dataEntryId, CriterionDataEntry dataEntry);

        /// <summary>
        /// Belirli bir kriter data entry'sini sil
        /// </summary>
        Task<bool> DeleteCriterionDataEntryAsync(string submissionId, string criterionLinkId, string dataEntryId);

        /// <summary>
        /// Kriter için tüm data entry'leri getir
        /// </summary>
        Task<List<CriterionDataEntry>?> GetCriterionDataEntriesAsync(string submissionId, string criterionLinkId);

        /// <summary>
        /// Submission'ın completion percentage'ini güncelle
        /// </summary>
        Task<bool> UpdateCompletionPercentageAsync(string submissionId, decimal percentage);

        #endregion

        #region Query Operations

        /// <summary>
        /// Akademisyenin belirli status'taki submission'larını getir
        /// </summary>
        Task<List<AcademicSubmissionDocument>> GetSubmissionsByStatusAsync(string academicianUserId, string status);

        /// <summary>
        /// Tüm submission'ları belirli status'a göre getir (Controller dashboard için)
        /// </summary>
        Task<List<AcademicSubmissionDocument>> GetAllSubmissionsByStatusAsync(string status);

        /// <summary>
        /// Belirli tarih aralığındaki submission'ları getir
        /// </summary>
        Task<List<AcademicSubmissionDocument>> GetSubmissionsByDateRangeAsync(string academicianUserId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Submission var mı kontrol et
        /// </summary>
        Task<bool> SubmissionExistsAsync(string academicianUserId, string formId);

        /// <summary>
        /// Son aktivite tarihini güncelle
        /// </summary>
        Task<bool> UpdateLastActivityAsync(string submissionId);

        #endregion

        #region Statistics

        /// <summary>
        /// Akademisyenin submission istatistiklerini getir
        /// </summary>
        Task<Dictionary<string, int>> GetSubmissionStatisticsAsync(string academicianUserId);

        /// <summary>
        /// Form için toplam submission sayısını getir
        /// </summary>
        Task<int> GetSubmissionCountByFormAsync(string formId);

        #endregion
    }
}
