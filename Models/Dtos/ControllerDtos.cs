using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.Dtos
{
    /// <summary>
    /// Controller dashboard DTO'su
    /// Epic 4 Controller Workflow için dashboard verileri
    /// </summary>
    public class ControllerDashboardDto
    {
        /// <summary>
        /// Pending submission sayısı
        /// </summary>
        public int PendingSubmissionsCount { get; set; }

        /// <summary>
        /// Bugün gelen submission sayısı
        /// </summary>
        public int TotalSubmissionsToday { get; set; }

        /// <summary>
        /// Bu hafta review edilen submission sayısı
        /// </summary>
        public int ReviewedThisWeek { get; set; }

        /// <summary>
        /// Ortalama review süresi (saat)
        /// </summary>
        public double AverageReviewTimeHours { get; set; }

        /// <summary>
        /// Son submission'lar (en fazla 5)
        /// </summary>
        public List<PendingSubmissionDto> RecentSubmissions { get; set; } = new();

        /// <summary>
        /// Controller istatistikleri
        /// </summary>
        public ControllerStatisticsDto Statistics { get; set; } = new();

        /// <summary>
        /// Deadline yaklaşan submission'lar
        /// </summary>
        public List<PendingSubmissionDto> UpcomingDeadlines { get; set; } = new();
    }

    /// <summary>
    /// Pending submission DTO'su
    /// Controller dashboard'unda gösterilecek submission özeti
    /// </summary>
    public class PendingSubmissionDto
    {
        /// <summary>
        /// Submission ID'si
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Submission AutoIncrement ID'si
        /// </summary>
        public int AutoIncrementId { get; set; }

        /// <summary>
        /// Akademisyen adı
        /// </summary>
        public string AcademicianName { get; set; } = string.Empty;

        /// <summary>
        /// Akademisyen email'i
        /// </summary>
        public string AcademicianEmail { get; set; } = string.Empty;

        /// <summary>
        /// Form adı
        /// </summary>
        public string FormName { get; set; } = string.Empty;

        /// <summary>
        /// Form ID'si
        /// </summary>
        public string FormId { get; set; } = string.Empty;

        /// <summary>
        /// Submission tarihi
        /// </summary>
        public DateTime SubmittedAt { get; set; }

        /// <summary>
        /// Evidence file sayısı
        /// </summary>
        public int EvidenceFileCount { get; set; }

        /// <summary>
        /// Bölüm adı
        /// </summary>
        public string Department { get; set; } = string.Empty;

        /// <summary>
        /// Akademik kadro
        /// </summary>
        public string AcademicCadre { get; set; } = string.Empty;

        /// <summary>
        /// Submission status'u
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Deadline tarihi
        /// </summary>
        public DateTime? Deadline { get; set; }

        /// <summary>
        /// Deadline'a kalan gün sayısı
        /// </summary>
        public int? DaysUntilDeadline { get; set; }

        /// <summary>
        /// Öncelik seviyesi (High, Medium, Low)
        /// </summary>
        public string Priority { get; set; } = "Medium";

        /// <summary>
        /// Akademisyen kullanıcı ID'si
        /// </summary>
        public string AcademicianUserId { get; set; } = string.Empty;

        /// <summary>
        /// Tamamlanma yüzdesi
        /// </summary>
        public decimal CompletionPercentage { get; set; }

        /// <summary>
        /// Son aktivite tarihi
        /// </summary>
        public DateTime LastActivityAt { get; set; }

        /// <summary>
        /// Atanmış controller ID'si
        /// </summary>
        public string? AssignedControllerId { get; set; }

        /// <summary>
        /// Review başlangıç tarihi
        /// </summary>
        public DateTime? ReviewStartedAt { get; set; }

        /// <summary>
        /// Tahmini review süresi (dakika)
        /// </summary>
        public int EstimatedReviewTime { get; set; }
    }

    /// <summary>
    /// Submission review DTO'su
    /// Detaylı submission review için kullanılır
    /// </summary>
    public class SubmissionReviewDto
    {
        /// <summary>
        /// Submission bilgileri
        /// </summary>
        public SubmissionDto Submission { get; set; } = new();

        /// <summary>
        /// Akademisyen profil bilgileri
        /// </summary>
        public AcademicianProfileDto Academician { get; set; } = new()
        {
            UniversityUserId = "",
            Name = "",
            Surname = ""
        };

        /// <summary>
        /// Evidence file'lar
        /// </summary>
        public List<EvidenceFileDto> EvidenceFiles { get; set; } = new();

        /// <summary>
        /// Criterion data'ları
        /// </summary>
        public List<CriterionDataDto> CriterionData { get; set; } = new();

        /// <summary>
        /// Form bilgileri
        /// </summary>
        public EvaluationFormDto Form { get; set; } = new()
        {
            Name = "",
            Status = ""
        };

        /// <summary>
        /// Review edilebilir mi?
        /// </summary>
        public bool CanReview { get; set; }

        /// <summary>
        /// Approve edilebilir mi?
        /// </summary>
        public bool CanApprove { get; set; }

        /// <summary>
        /// Reject edilebilir mi?
        /// </summary>
        public bool CanReject { get; set; }

        /// <summary>
        /// Review başlangıç tarihi
        /// </summary>
        public DateTime? ReviewStartedAt { get; set; }

        /// <summary>
        /// Review eden controller ID'si
        /// </summary>
        public string? ReviewedByControllerId { get; set; }
    }

    /// <summary>
    /// Controller statistics DTO'su
    /// </summary>
    public class ControllerStatisticsDto
    {
        /// <summary>
        /// Toplam review edilen submission sayısı
        /// </summary>
        public int TotalReviewed { get; set; }

        /// <summary>
        /// Approve edilen submission sayısı
        /// </summary>
        public int TotalApproved { get; set; }

        /// <summary>
        /// Reject edilen submission sayısı
        /// </summary>
        public int TotalRejected { get; set; }

        /// <summary>
        /// Bu ay review edilen sayı
        /// </summary>
        public int ReviewedThisMonth { get; set; }

        /// <summary>
        /// Bu hafta review edilen sayı
        /// </summary>
        public int ReviewedThisWeek { get; set; }

        /// <summary>
        /// Bugün review edilen sayı
        /// </summary>
        public int ReviewedToday { get; set; }

        /// <summary>
        /// Ortalama review süresi (saat)
        /// </summary>
        public double AverageReviewTimeHours { get; set; }

        /// <summary>
        /// Approval oranı (%)
        /// </summary>
        public double ApprovalRate { get; set; }

        /// <summary>
        /// En hızlı review süresi (dakika)
        /// </summary>
        public int FastestReviewMinutes { get; set; }

        /// <summary>
        /// En yavaş review süresi (saat)
        /// </summary>
        public int SlowestReviewHours { get; set; }
    }

    /// <summary>
    /// Criterion data DTO'su - Review için
    /// </summary>
    public class CriterionDataDto
    {
        /// <summary>
        /// Criterion link ID'si
        /// </summary>
        public string CriterionLinkId { get; set; } = string.Empty;

        /// <summary>
        /// Criterion adı
        /// </summary>
        public string CriterionName { get; set; } = string.Empty;

        /// <summary>
        /// Criterion tipi (Static/Dynamic)
        /// </summary>
        public string CriterionType { get; set; } = string.Empty;

        /// <summary>
        /// Data entry'leri
        /// </summary>
        public List<CriterionDataEntryDto> DataEntries { get; set; } = new();

        /// <summary>
        /// Tamamlanma durumu
        /// </summary>
        public bool IsCompleted { get; set; }

        /// <summary>
        /// Son güncelleme tarihi
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// Notlar
        /// </summary>
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Submission approval DTO'su
    /// </summary>
    public class SubmissionApprovalDto
    {
        /// <summary>
        /// Submission ID'si
        /// </summary>
        [Required]
        public string SubmissionId { get; set; } = string.Empty;

        /// <summary>
        /// Onay yorumları (opsiyonel)
        /// </summary>
        [StringLength(2000)]
        public string? Comments { get; set; }
    }

    /// <summary>
    /// Submission rejection DTO'su
    /// </summary>
    public class SubmissionRejectionDto
    {
        /// <summary>
        /// Submission ID'si
        /// </summary>
        [Required]
        public string SubmissionId { get; set; } = string.Empty;

        /// <summary>
        /// Red yorumları (zorunlu)
        /// </summary>
        [Required]
        [StringLength(2000, MinimumLength = 10)]
        public string Comments { get; set; } = string.Empty;
    }
}
