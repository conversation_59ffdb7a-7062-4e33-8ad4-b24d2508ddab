using MongoDB.Driver;
using MongoDB.Bson;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.MongoDocuments;
using AcademicPerformance.Services.Interfaces;

namespace AcademicPerformance.Stores
{
    /// <summary>
    /// Submission data access implementation - MongoDB operations
    /// </summary>
    public class SubmissionStore : ISubmissionStore
    {
        private readonly IMongoDbService _mongoDbService;
        private readonly ILogger<SubmissionStore> _logger;

        public SubmissionStore(IMongoDbService mongoDbService, ILogger<SubmissionStore> logger)
        {
            _mongoDbService = mongoDbService;
            _logger = logger;
        }

        #region Submission CRUD Operations

        public async Task<AcademicSubmissionDocument?> GetSubmissionByFormIdAsync(string academicianUserId, string formId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.And(
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.AcademicianUserId, academicianUserId),
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.FormId, formId)
                );

                return await _mongoDbService.GetDocumentAsync(filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submission by form ID {FormId} for user {UserId}", formId, academicianUserId);
                throw;
            }
        }

        public async Task<AcademicSubmissionDocument?> GetSubmissionByIdAsync(string submissionId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId);
                return await _mongoDbService.GetDocumentAsync(filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submission by ID {SubmissionId}", submissionId);
                throw;
            }
        }

        public async Task<List<AcademicSubmissionDocument>> GetSubmissionsByAcademicianAsync(string academicianUserId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.AcademicianUserId, academicianUserId);
                var sort = Builders<AcademicSubmissionDocument>.Sort.Descending(s => s.LastActivityAt);

                return await _mongoDbService.GetDocumentsAsync(filter, sort);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submissions for user {UserId}", academicianUserId);
                throw;
            }
        }

        public async Task<AcademicSubmissionDocument> CreateSubmissionAsync(AcademicSubmissionDocument document)
        {
            try
            {
                document.CreatedAt = DateTime.UtcNow;
                document.LastActivityAt = DateTime.UtcNow;

                await _mongoDbService.InsertDocumentAsync(document);
                return document;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating submission for form {FormId} and user {UserId}",
                    document.FormId, document.AcademicianUserId);
                throw;
            }
        }

        public async Task<bool> UpdateSubmissionAsync(AcademicSubmissionDocument document)
        {
            try
            {
                document.UpdatedAt = DateTime.UtcNow;
                document.LastActivityAt = DateTime.UtcNow;

                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, document.Id);
                var result = await _mongoDbService.ReplaceDocumentAsync(filter, document);

                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating submission {SubmissionId}", document.Id);
                throw;
            }
        }

        public async Task<bool> DeleteSubmissionAsync(string submissionId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId);
                var result = await _mongoDbService.DeleteDocumentAsync(filter);

                return result.DeletedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting submission {SubmissionId}", submissionId);
                throw;
            }
        }

        public async Task<bool> UpdateSubmissionStatusAsync(string submissionId, string status, DateTime? submittedAt = null)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId);

                var updateBuilder = Builders<AcademicSubmissionDocument>.Update
                    .Set(s => s.Status, status)
                    .Set(s => s.UpdatedAt, DateTime.UtcNow)
                    .Set(s => s.LastActivityAt, DateTime.UtcNow);

                if (submittedAt.HasValue)
                {
                    updateBuilder = updateBuilder.Set(s => s.SubmittedAt, submittedAt.Value);
                }

                var result = await _mongoDbService.UpdateDocumentAsync(filter, updateBuilder);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating submission status {SubmissionId} to {Status}", submissionId, status);
                throw;
            }
        }

        #endregion

        #region Criterion Data Operations

        public async Task<bool> UpsertCriterionDataAsync(string submissionId, string criterionLinkId, SubmissionCriterionData criterionData)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId);

                // CriteriaData array'inde bu criterionLinkId var mı kontrol et
                var arrayFilter = Builders<AcademicSubmissionDocument>.Filter.And(
                    filter,
                    Builders<AcademicSubmissionDocument>.Filter.ElemMatch(s => s.CriteriaData,
                        cd => cd.CriterionLinkId == criterionLinkId)
                );

                var existingDoc = await _mongoDbService.GetDocumentAsync(arrayFilter);

                UpdateDefinition<AcademicSubmissionDocument> update;

                if (existingDoc != null)
                {
                    // Güncelle
                    update = Builders<AcademicSubmissionDocument>.Update
                        .Set("CriteriaData.$.DataEntries", criterionData.DataEntries)
                        .Set("CriteriaData.$.IsCompleted", criterionData.IsCompleted)
                        .Set("CriteriaData.$.LastUpdated", DateTime.UtcNow)
                        .Set("CriteriaData.$.Notes", criterionData.Notes)
                        .Set(s => s.UpdatedAt, DateTime.UtcNow)
                        .Set(s => s.LastActivityAt, DateTime.UtcNow);

                    var result = await _mongoDbService.UpdateDocumentAsync(arrayFilter, update);
                    return result.ModifiedCount > 0;
                }
                else
                {
                    // Ekle
                    criterionData.LastUpdated = DateTime.UtcNow;
                    update = Builders<AcademicSubmissionDocument>.Update
                        .Push(s => s.CriteriaData, criterionData)
                        .Set(s => s.UpdatedAt, DateTime.UtcNow)
                        .Set(s => s.LastActivityAt, DateTime.UtcNow);

                    var result = await _mongoDbService.UpdateDocumentAsync(filter, update);
                    return result.ModifiedCount > 0;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error upserting criterion data for submission {SubmissionId}, criterion {CriterionLinkId}",
                    submissionId, criterionLinkId);
                throw;
            }
        }

        public async Task<bool> UpdateCriterionDataEntryAsync(string submissionId, string criterionLinkId, string dataEntryId, CriterionDataEntry dataEntry)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.And(
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId),
                    Builders<AcademicSubmissionDocument>.Filter.ElemMatch(s => s.CriteriaData,
                        cd => cd.CriterionLinkId == criterionLinkId),
                    Builders<AcademicSubmissionDocument>.Filter.ElemMatch("CriteriaData.DataEntries",
                        Builders<CriterionDataEntry>.Filter.Eq(de => de.Id, dataEntryId))
                );

                dataEntry.UpdatedAt = DateTime.UtcNow;

                var update = Builders<AcademicSubmissionDocument>.Update
                    .Set("CriteriaData.$[criterion].DataEntries.$[entry]", dataEntry)
                    .Set(s => s.UpdatedAt, DateTime.UtcNow)
                    .Set(s => s.LastActivityAt, DateTime.UtcNow);

                var arrayFilters = new[]
                {
                    new BsonDocumentArrayFilterDefinition<BsonDocument>(new BsonDocument("criterion.CriterionLinkId", criterionLinkId)),
                    new BsonDocumentArrayFilterDefinition<BsonDocument>(new BsonDocument("entry.Id", dataEntryId))
                };

                var updateOptions = new UpdateOptions { ArrayFilters = arrayFilters };
                var result = await _mongoDbService.UpdateDocumentAsync(filter, update, updateOptions);

                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating criterion data entry {DataEntryId} for submission {SubmissionId}",
                    dataEntryId, submissionId);
                throw;
            }
        }

        public async Task<bool> DeleteCriterionDataEntryAsync(string submissionId, string criterionLinkId, string dataEntryId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.And(
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId),
                    Builders<AcademicSubmissionDocument>.Filter.ElemMatch(s => s.CriteriaData,
                        cd => cd.CriterionLinkId == criterionLinkId)
                );

                var update = Builders<AcademicSubmissionDocument>.Update
                    .PullFilter("CriteriaData.$.DataEntries",
                        Builders<CriterionDataEntry>.Filter.Eq(de => de.Id, dataEntryId))
                    .Set(s => s.UpdatedAt, DateTime.UtcNow)
                    .Set(s => s.LastActivityAt, DateTime.UtcNow);

                var result = await _mongoDbService.UpdateDocumentAsync(filter, update);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting criterion data entry {DataEntryId} for submission {SubmissionId}",
                    dataEntryId, submissionId);
                throw;
            }
        }

        public async Task<List<CriterionDataEntry>?> GetCriterionDataEntriesAsync(string submissionId, string criterionLinkId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId);
                var submission = await _mongoDbService.GetDocumentAsync(filter);

                var criterionData = submission?.CriteriaData?.FirstOrDefault(cd => cd.CriterionLinkId == criterionLinkId);
                return criterionData?.DataEntries;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting criterion data entries for submission {SubmissionId}, criterion {CriterionLinkId}",
                    submissionId, criterionLinkId);
                throw;
            }
        }

        public async Task<bool> UpdateCompletionPercentageAsync(string submissionId, decimal percentage)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId);
                var update = Builders<AcademicSubmissionDocument>.Update
                    .Set(s => s.CompletionPercentage, percentage)
                    .Set(s => s.UpdatedAt, DateTime.UtcNow)
                    .Set(s => s.LastActivityAt, DateTime.UtcNow);

                var result = await _mongoDbService.UpdateDocumentAsync(filter, update);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating completion percentage for submission {SubmissionId}", submissionId);
                throw;
            }
        }

        #endregion

        #region Query Operations

        public async Task<List<AcademicSubmissionDocument>> GetSubmissionsByStatusAsync(string academicianUserId, string status)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.And(
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.AcademicianUserId, academicianUserId),
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Status, status)
                );

                var sort = Builders<AcademicSubmissionDocument>.Sort.Descending(s => s.LastActivityAt);
                return await _mongoDbService.GetDocumentsAsync(filter, sort);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submissions by status {Status} for user {UserId}", status, academicianUserId);
                throw;
            }
        }

        public async Task<List<AcademicSubmissionDocument>> GetSubmissionsByDateRangeAsync(string academicianUserId, DateTime startDate, DateTime endDate)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.And(
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.AcademicianUserId, academicianUserId),
                    Builders<AcademicSubmissionDocument>.Filter.Gte(s => s.CreatedAt, startDate),
                    Builders<AcademicSubmissionDocument>.Filter.Lte(s => s.CreatedAt, endDate)
                );

                var sort = Builders<AcademicSubmissionDocument>.Sort.Descending(s => s.CreatedAt);
                return await _mongoDbService.GetDocumentsAsync(filter, sort);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submissions by date range for user {UserId}", academicianUserId);
                throw;
            }
        }

        public async Task<bool> SubmissionExistsAsync(string academicianUserId, string formId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.And(
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.AcademicianUserId, academicianUserId),
                    Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.FormId, formId)
                );

                var count = await _mongoDbService.CountDocumentsAsync(filter);
                return count > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking submission existence for form {FormId} and user {UserId}", formId, academicianUserId);
                throw;
            }
        }

        public async Task<List<AcademicSubmissionDocument>> GetAllSubmissionsByStatusAsync(string status)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Status, status);
                var sort = Builders<AcademicSubmissionDocument>.Sort.Descending(s => s.LastActivityAt);

                return await _mongoDbService.GetDocumentsAsync(filter, sort);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all submissions by status {Status}", status);
                throw;
            }
        }

        public async Task<bool> UpdateLastActivityAsync(string submissionId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.Id, submissionId);
                var update = Builders<AcademicSubmissionDocument>.Update
                    .Set(s => s.LastActivityAt, DateTime.UtcNow);

                var result = await _mongoDbService.UpdateDocumentAsync(filter, update);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating last activity for submission {SubmissionId}", submissionId);
                throw;
            }
        }

        #endregion

        #region Statistics

        public async Task<Dictionary<string, int>> GetSubmissionStatisticsAsync(string academicianUserId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.AcademicianUserId, academicianUserId);
                var submissions = await _mongoDbService.GetDocumentsAsync(filter);

                var stats = new Dictionary<string, int>
                {
                    ["Total"] = submissions.Count,
                    ["Draft"] = submissions.Count(s => s.Status == "Draft"),
                    ["InProgress"] = submissions.Count(s => s.Status == "InProgress"),
                    ["Submitted"] = submissions.Count(s => s.Status == "Submitted"),
                    ["UnderReview"] = submissions.Count(s => s.Status == "UnderReview")
                };

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submission statistics for user {UserId}", academicianUserId);
                throw;
            }
        }

        public async Task<int> GetSubmissionCountByFormAsync(string formId)
        {
            try
            {
                var filter = Builders<AcademicSubmissionDocument>.Filter.Eq(s => s.FormId, formId);
                return (int)await _mongoDbService.CountDocumentsAsync(filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submission count for form {FormId}", formId);
                throw;
            }
        }

        #endregion
    }
}
