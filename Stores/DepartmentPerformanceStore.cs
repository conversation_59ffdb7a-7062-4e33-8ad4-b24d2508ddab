using AcademicPerformance.DbContexts;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Extensions;
using AcademicPerformance.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace AcademicPerformance.Stores
{
    /// <summary>
    /// Bölüm performans veri katmanı implementasyonu
    /// </summary>
    public class DepartmentPerformanceStore : IDepartmentPerformanceStore
    {
        private readonly AcademicPerformanceDbContext _context;
        private readonly ILogger<DepartmentPerformanceStore> _logger;
        public DepartmentPerformanceStore(
            AcademicPerformanceDbContext context,
            ILogger<DepartmentPerformanceStore> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region CRUD Operations

        /// <summary>
        /// Bölüm performans entity'si oluştur
        /// </summary>
        public async Task<DepartmentPerformanceEntity> CreateAsync(DepartmentPerformanceEntity entity)
        {
            try
            {
                _context.DepartmentPerformances.Add(entity);
                await _context.SaveChangesAsync();
                return entity;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm performans entity'si oluşturulurken hata - ID: {Id}", entity.Id);
                throw;
            }
        }

        /// <summary>
        /// Bölüm performans entity'sini güncelle
        /// </summary>
        public async Task<bool> UpdateAsync(DepartmentPerformanceEntity entity)
        {
            try
            {
                _context.DepartmentPerformances.Update(entity);
                var result = await _context.SaveChangesAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm performans entity'si güncellenirken hata - ID: {Id}", entity.Id);
                throw;
            }
        }

        /// <summary>
        /// Bölüm performans entity'sini sil
        /// </summary>
        public async Task<bool> DeleteAsync(string id)
        {
            try
            {
                var entity = await _context.DepartmentPerformances.FindAsync(id);
                if (entity == null) return false;

                _context.DepartmentPerformances.Remove(entity);
                var result = await _context.SaveChangesAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm performans entity'si silinirken hata - ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Bölüm performans entity'sini ID ile getir
        /// </summary>
        public async Task<DepartmentPerformanceEntity?> GetByIdAsync(string id)
        {
            try
            {
                return await _context.DepartmentPerformances
                    .FirstOrDefaultAsync(x => x.Id == id && !x.Deleted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm performans entity'si getirilirken hata - ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Bölüm performans entity'lerini filtreli getir
        /// </summary>
        public async Task<PagedListDto<DepartmentPerformanceEntity>> GetPagedAsync(
            PagedListCo<DepartmentPerformanceFilterCo> co)
        {
            try
            {
                var query = _context.DepartmentPerformances
                    .Where(x => !x.Deleted)
                    .AsQueryable();

                // Apply filters
                if (co.Criteria != null)
                {
                    var filter = co.Criteria;

                    if (!string.IsNullOrEmpty(filter.DepartmentId))
                    {
                        query = query.Where(x => x.DepartmentId == filter.DepartmentId);
                    }

                    if (!string.IsNullOrEmpty(filter.FacultyId))
                    {
                        query = query.Where(x => x.FacultyId == filter.FacultyId);
                    }

                    if (!string.IsNullOrEmpty(filter.Period))
                    {
                        query = query.Where(x => x.Period == filter.Period);
                    }

                    if (filter.StartDate.HasValue)
                    {
                        query = query.Where(x => x.EvaluationDate >= filter.StartDate.Value);
                    }

                    if (filter.EndDate.HasValue)
                    {
                        query = query.Where(x => x.EvaluationDate <= filter.EndDate.Value);
                    }

                    if (filter.MinOverallScore.HasValue)
                    {
                        query = query.Where(x => x.OverallScore >= (decimal)filter.MinOverallScore.Value);
                    }

                    if (filter.MaxOverallScore.HasValue)
                    {
                        query = query.Where(x => x.OverallScore <= (decimal)filter.MaxOverallScore.Value);
                    }

                    if (!string.IsNullOrEmpty(filter.Status))
                    {
                        query = query.Where(x => x.Status == filter.Status);
                    }

                    if (filter.IsActive.HasValue)
                    {
                        query = query.Where(x => x.IsActive == filter.IsActive.Value);
                    }
                }

                // Apply sorting
                if (!string.IsNullOrEmpty(co.Sort))
                {
                    query = co.Sort.ToLower() switch
                    {
                        "period" => query.OrderBy(x => x.Period),
                        "period_desc" => query.OrderByDescending(x => x.Period),
                        "overallscore" => query.OrderBy(x => x.OverallScore),
                        "overallscore_desc" => query.OrderByDescending(x => x.OverallScore),
                        "evaluationdate" => query.OrderBy(x => x.EvaluationDate),
                        "evaluationdate_desc" => query.OrderByDescending(x => x.EvaluationDate),
                        "ranking" => query.OrderBy(x => x.Ranking),
                        "ranking_desc" => query.OrderByDescending(x => x.Ranking),
                        _ => query.OrderByDescending(x => x.EvaluationDate)
                    };
                }
                else
                {
                    query = query.OrderByDescending(x => x.EvaluationDate);
                }

                // Get total count
                var totalCount = await query.CountAsync();

                // Apply pagination
                var items = await query
                    .Skip(co.Pager.Skip)
                    .Take(co.Pager.Size)
                    .ToListAsync();

                return new PagedListDto<DepartmentPerformanceEntity>
                {
                    Data = items,
                    Count = items.Count,
                    TotalCount = totalCount,
                    Page = co.Pager.Page,
                    Size = co.Pager.Size
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Sayfalanmış bölüm performans verileri getirilirken hata");
                throw;
            }
        }

        #endregion

        #region Query Operations

        /// <summary>
        /// Bölümün belirli dönemdeki performans kaydını getir
        /// </summary>
        public async Task<DepartmentPerformanceEntity?> GetByDepartmentAndPeriodAsync(
            string departmentId,
            string period)
        {
            try
            {
                return await _context.DepartmentPerformances
                    .FirstOrDefaultAsync(x => x.DepartmentId == departmentId &&
                                            x.Period == period &&
                                            !x.Deleted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm dönem performansı getirilirken hata - Department: {DepartmentId}, Period: {Period}",
                    departmentId, period);
                throw;
            }
        }

        /// <summary>
        /// Bölümün tüm performans kayıtlarını getir
        /// </summary>
        public async Task<List<DepartmentPerformanceEntity>> GetByDepartmentAsync(
            string departmentId,
            int? limit = null)
        {
            try
            {
                var query = _context.DepartmentPerformances
                    .Where(x => x.DepartmentId == departmentId && !x.Deleted)
                    .OrderByDescending(x => x.EvaluationDate);

                if (limit.HasValue)
                {
                    query = (IOrderedQueryable<DepartmentPerformanceEntity>)query.Take(limit.Value);
                }

                return await query.ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm performans kayıtları getirilirken hata - Department: {DepartmentId}", departmentId);
                throw;
            }
        }

        /// <summary>
        /// Fakültenin tüm bölüm performanslarını getir
        /// </summary>
        public async Task<List<DepartmentPerformanceEntity>> GetByFacultyAsync(
            string facultyId,
            string? period = null)
        {
            try
            {
                var query = _context.DepartmentPerformances
                    .Where(x => x.FacultyId == facultyId && !x.Deleted);

                if (!string.IsNullOrEmpty(period))
                {
                    query = query.Where(x => x.Period == period);
                }

                return await query
                    .OrderByDescending(x => x.OverallScore)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Fakülte performans kayıtları getirilirken hata - Faculty: {FacultyId}", facultyId);
                throw;
            }
        }

        /// <summary>
        /// Belirli dönemdeki tüm bölüm performanslarını getir
        /// </summary>
        public async Task<List<DepartmentPerformanceEntity>> GetByPeriodAsync(
            string period,
            string? facultyId = null)
        {
            try
            {
                var query = _context.DepartmentPerformances
                    .Where(x => x.Period == period && !x.Deleted);

                if (!string.IsNullOrEmpty(facultyId))
                {
                    query = query.Where(x => x.FacultyId == facultyId);
                }

                return await query
                    .OrderByDescending(x => x.OverallScore)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Dönem performans kayıtları getirilirken hata - Period: {Period}", period);
                throw;
            }
        }

        /// <summary>
        /// Bölümün son N dönemlik performanslarını getir
        /// </summary>
        public async Task<List<DepartmentPerformanceEntity>> GetRecentPerformancesAsync(
            string departmentId,
            int periodCount)
        {
            try
            {
                return await _context.DepartmentPerformances
                    .Where(x => x.DepartmentId == departmentId && !x.Deleted)
                    .OrderByDescending(x => x.EvaluationDate)
                    .Take(periodCount)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Son dönem performansları getirilirken hata - Department: {DepartmentId}", departmentId);
                throw;
            }
        }

        #endregion

        #region Statistical Operations

        /// <summary>
        /// Bölüm performans istatistiklerini hesapla
        /// </summary>
        public async Task<DepartmentStatisticsDto> CalculateStatisticsAsync(
            string departmentId,
            int periodCount)
        {
            try
            {
                var performances = await GetRecentPerformancesAsync(departmentId, periodCount);

                if (!performances.Any())
                {
                    return new DepartmentStatisticsDto
                    {
                        TotalEvaluations = 0,
                        AverageScore = 0
                    };
                }

                var statistics = new DepartmentStatisticsDto
                {
                    TotalEvaluations = performances.Count,
                    AverageScore = (double)performances.Average(p => p.OverallScore),
                    HighestScore = (double)performances.Max(p => p.OverallScore),
                    LowestScore = (double)performances.Min(p => p.OverallScore),
                    YearOverYearGrowth = CalculateYearOverYearGrowth(performances),
                    CategoryAverages = new Dictionary<string, double>
                    {
                        ["AcademicStaffPerformance"] = (double)performances.Average(p => p.AcademicStaffPerformance),
                        ["ResearchPerformance"] = (double)performances.Average(p => p.ResearchPerformance),
                        ["PublicationPerformance"] = (double)performances.Average(p => p.PublicationPerformance),
                        ["StudentSatisfactionScore"] = (double)performances.Average(p => p.StudentSatisfactionScore),
                        ["InfrastructureScore"] = (double)performances.Average(p => p.InfrastructureScore),
                        ["BudgetEfficiencyScore"] = (double)performances.Average(p => p.BudgetEfficiencyScore)
                    },
                    MonthlySubmissions = CalculateMonthlySubmissions(performances)
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm istatistikleri hesaplanırken hata - Department: {DepartmentId}", departmentId);
                throw;
            }
        }

        /// <summary>
        /// Fakülte geneli performans ortalamasını hesapla
        /// </summary>
        public async Task<Dictionary<string, double>> CalculateFacultyAveragesAsync(
            string facultyId,
            string period)
        {
            try
            {
                var performances = await GetByFacultyAsync(facultyId, period);

                if (!performances.Any())
                {
                    return new Dictionary<string, double>();
                }

                return new Dictionary<string, double>
                {
                    ["OverallScore"] = (double)performances.Average(p => p.OverallScore),
                    ["AcademicStaffPerformance"] = (double)performances.Average(p => p.AcademicStaffPerformance),
                    ["ResearchPerformance"] = (double)performances.Average(p => p.ResearchPerformance),
                    ["PublicationPerformance"] = (double)performances.Average(p => p.PublicationPerformance),
                    ["StudentSatisfactionScore"] = (double)performances.Average(p => p.StudentSatisfactionScore),
                    ["InfrastructureScore"] = (double)performances.Average(p => p.InfrastructureScore),
                    ["BudgetEfficiencyScore"] = (double)performances.Average(p => p.BudgetEfficiencyScore),
                    ["CompletionRate"] = (double)performances.Average(p => p.CompletionRate)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Fakülte ortalamaları hesaplanırken hata - Faculty: {FacultyId}", facultyId);
                throw;
            }
        }

        /// <summary>
        /// Genel performans benchmark'larını hesapla
        /// </summary>
        public async Task<DepartmentBenchmarkDto> CalculateBenchmarkAsync(
            string period,
            string? facultyId = null)
        {
            try
            {
                var performances = await GetByPeriodAsync(period, facultyId);

                if (!performances.Any())
                {
                    return new DepartmentBenchmarkDto
                    {
                        AverageOverallScore = 0
                    };
                }

                var orderedPerformances = performances.OrderByDescending(p => p.OverallScore).ToList();
                var scores = performances.Select(p => (double)p.OverallScore).ToList();

                return new DepartmentBenchmarkDto
                {
                    AverageOverallScore = scores.Average(),
                    TopPerformerScore = scores.Max(),
                    BottomPerformerScore = scores.Min(),
                    StandardDeviation = CalculateStandardDeviation(scores),
                    TopPerformingDepartment = orderedPerformances.First().DepartmentName ?? "",
                    BottomPerformingDepartment = orderedPerformances.Last().DepartmentName ?? ""
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Benchmark hesaplanırken hata - Period: {Period}", period);
                throw;
            }
        }

        /// <summary>
        /// Bölüm sıralamasını hesapla
        /// </summary>
        public async Task<List<DepartmentRankingItemDto>> CalculateRankingAsync(
            string period,
            string metric = "OverallScore",
            string? facultyId = null)
        {
            try
            {
                var performances = await GetByPeriodAsync(period, facultyId);

                if (!performances.Any())
                {
                    return new List<DepartmentRankingItemDto>();
                }

                // Order by the specified metric
                var orderedPerformances = metric.ToLower() switch
                {
                    "overallscore" => performances.OrderByDescending(p => p.OverallScore).AsEnumerable(),
                    "academicstaffperformance" => performances.OrderByDescending(p => p.AcademicStaffPerformance).AsEnumerable(),
                    "researchperformance" => performances.OrderByDescending(p => p.ResearchPerformance).AsEnumerable(),
                    "publicationperformance" => performances.OrderByDescending(p => p.PublicationPerformance).AsEnumerable(),
                    "studentsatisfactionscore" => performances.OrderByDescending(p => p.StudentSatisfactionScore).AsEnumerable(),
                    "completionrate" => performances.OrderByDescending(p => p.CompletionRate).AsEnumerable(),
                    _ => performances.OrderByDescending(p => p.OverallScore).AsEnumerable()
                };

                var rankings = new List<DepartmentRankingItemDto>();
                var performanceList = orderedPerformances.ToList();
                var topScore = GetMetricValue(performanceList.First(), metric);

                for (int i = 0; i < performanceList.Count; i++)
                {
                    var performance = performanceList[i];
                    var score = GetMetricValue(performance, metric);
                    var nextScore = i < performanceList.Count - 1 ? GetMetricValue(performanceList[i + 1], metric) : 0;

                    rankings.Add(new DepartmentRankingItemDto
                    {
                        DepartmentId = performance.DepartmentId,
                        DepartmentName = performance.DepartmentName ?? "",
                        Score = score,
                        Ranking = i + 1,
                        ScoreGapToNext = score - nextScore,
                        ScoreGapToTop = topScore - score
                    });
                }

                return rankings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Sıralama hesaplanırken hata - Period: {Period}", period);
                throw;
            }
        }

        #endregion

        #region Trend Analysis

        /// <summary>
        /// Bölüm trend verilerini hesapla
        /// </summary>
        public async Task<List<DepartmentTrendDataDto>> CalculateTrendDataAsync(
            string departmentId,
            int periodCount,
            List<string> metrics)
        {
            try
            {
                var performances = await GetRecentPerformancesAsync(departmentId, periodCount);

                return performances.Select(p => new DepartmentTrendDataDto
                {
                    Period = p.Period,
                    EvaluationDate = p.EvaluationDate,
                    OverallScore = (double)p.OverallScore,
                    AcademicStaffPerformance = (double)p.AcademicStaffPerformance,
                    ResearchPerformance = (double)p.ResearchPerformance,
                    PublicationPerformance = (double)p.PublicationPerformance,
                    StudentSatisfactionScore = (double)p.StudentSatisfactionScore,
                    Ranking = p.Ranking ?? 0
                }).OrderBy(t => t.EvaluationDate).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Trend verileri hesaplanırken hata - Department: {DepartmentId}", departmentId);
                throw;
            }
        }

        /// <summary>
        /// Bölüm büyüme oranını hesapla
        /// </summary>
        public async Task<double> CalculateGrowthRateAsync(
            string departmentId,
            string currentPeriod,
            string previousPeriod)
        {
            try
            {
                var currentPerformance = await GetByDepartmentAndPeriodAsync(departmentId, currentPeriod);
                var previousPerformance = await GetByDepartmentAndPeriodAsync(departmentId, previousPeriod);

                if (currentPerformance == null || previousPerformance == null || previousPerformance.OverallScore == 0)
                {
                    return 0;
                }

                var growthRate = ((double)(currentPerformance.OverallScore - previousPerformance.OverallScore) /
                                 (double)previousPerformance.OverallScore) * 100;

                return Math.Round(growthRate, 2);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Büyüme oranı hesaplanırken hata - Department: {DepartmentId}", departmentId);
                throw;
            }
        }

        /// <summary>
        /// Çoklu bölüm karşılaştırma verilerini getir
        /// </summary>
        public async Task<Dictionary<string, Dictionary<string, double>>> GetComparisonDataAsync(
            List<string> departmentIds,
            string period,
            List<string> metrics)
        {
            try
            {
                var result = new Dictionary<string, Dictionary<string, double>>();

                foreach (var departmentId in departmentIds)
                {
                    var performance = await GetByDepartmentAndPeriodAsync(departmentId, period);
                    if (performance != null)
                    {
                        var departmentData = new Dictionary<string, double>();

                        foreach (var metric in metrics)
                        {
                            departmentData[metric] = GetMetricValue(performance, metric);
                        }

                        result[departmentId] = departmentData;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Karşılaştırma verileri getirilirken hata");
                throw;
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Yıllık büyüme oranını hesapla
        /// </summary>
        private static double CalculateYearOverYearGrowth(List<DepartmentPerformanceEntity> performances)
        {
            if (performances.Count < 2) return 0;

            var orderedPerformances = performances.OrderBy(p => p.EvaluationDate).ToList();
            var latest = orderedPerformances.Last();
            var previous = orderedPerformances[orderedPerformances.Count - 2];

            if (previous.OverallScore == 0) return 0;

            return ((double)(latest.OverallScore - previous.OverallScore) / (double)previous.OverallScore) * 100;
        }

        /// <summary>
        /// Aylık gönderim istatistiklerini hesapla
        /// </summary>
        private static Dictionary<string, int> CalculateMonthlySubmissions(List<DepartmentPerformanceEntity> performances)
        {
            return performances
                .GroupBy(p => p.EvaluationDate.ToString("yyyy-MM"))
                .ToDictionary(g => g.Key, g => g.Sum(p => p.CompletedSubmissions + p.PendingSubmissions));
        }

        /// <summary>
        /// Standart sapma hesapla
        /// </summary>
        private static double CalculateStandardDeviation(List<double> values)
        {
            if (values.Count == 0) return 0;

            var average = values.Average();
            var sumOfSquares = values.Sum(x => Math.Pow(x - average, 2));
            return Math.Sqrt(sumOfSquares / values.Count);
        }

        /// <summary>
        /// Trend yönünü hesapla
        /// </summary>
        private static string CalculateTrendDirection(List<DepartmentPerformanceEntity> performances)
        {
            if (performances.Count < 2) return "Stable";

            var orderedPerformances = performances.OrderBy(p => p.EvaluationDate).ToList();
            var recentScores = orderedPerformances.TakeLast(3).Select(p => (double)p.OverallScore).ToList();

            if (recentScores.Count < 2) return "Stable";

            var isImproving = recentScores.Last() > recentScores.First();
            var isConsistent = recentScores.Zip(recentScores.Skip(1), (a, b) => b > a).All(x => x) ||
                              recentScores.Zip(recentScores.Skip(1), (a, b) => b < a).All(x => x);

            if (isConsistent)
            {
                return isImproving ? "Improving" : "Declining";
            }

            return "Stable";
        }

        /// <summary>
        /// Medyan hesapla
        /// </summary>
        private static double CalculateMedian(IEnumerable<double> values)
        {
            var sortedValues = values.OrderBy(x => x).ToList();
            var count = sortedValues.Count;

            if (count == 0) return 0;
            if (count % 2 == 0)
            {
                return (sortedValues[count / 2 - 1] + sortedValues[count / 2]) / 2;
            }
            else
            {
                return sortedValues[count / 2];
            }
        }

        /// <summary>
        /// Percentile hesapla
        /// </summary>
        private static double CalculatePercentile(IEnumerable<double> values, double percentile)
        {
            var sortedValues = values.OrderBy(x => x).ToList();
            var count = sortedValues.Count;

            if (count == 0) return 0;

            var index = (percentile / 100.0) * (count - 1);
            var lowerIndex = (int)Math.Floor(index);
            var upperIndex = (int)Math.Ceiling(index);

            if (lowerIndex == upperIndex)
            {
                return sortedValues[lowerIndex];
            }

            var weight = index - lowerIndex;
            return sortedValues[lowerIndex] * (1 - weight) + sortedValues[upperIndex] * weight;
        }

        /// <summary>
        /// Metrik değerini getir
        /// </summary>
        private static double GetMetricValue(DepartmentPerformanceEntity performance, string metric)
        {
            return metric.ToLower() switch
            {
                "overallscore" => (double)performance.OverallScore,
                "academicstaffperformance" => (double)performance.AcademicStaffPerformance,
                "researchperformance" => (double)performance.ResearchPerformance,
                "publicationperformance" => (double)performance.PublicationPerformance,
                "studentsatisfactionscore" => (double)performance.StudentSatisfactionScore,
                "infrastructurescore" => (double)performance.InfrastructureScore,
                "budgetefficiencyscore" => (double)performance.BudgetEfficiencyScore,
                "completionrate" => (double)performance.CompletionRate,
                _ => (double)performance.OverallScore
            };
        }

        #endregion

        #region Aggregation Operations

        /// <summary>
        /// Bölüm performans verilerini topla
        /// </summary>
        public async Task<DepartmentPerformanceAggregateDto> AggregatePerformanceDataAsync(
            string departmentId,
            string startPeriod,
            string endPeriod)
        {
            try
            {
                var performances = await _context.DepartmentPerformances
                    .Where(x => x.DepartmentId == departmentId &&
                               string.Compare(x.Period, startPeriod) >= 0 &&
                               string.Compare(x.Period, endPeriod) <= 0 &&
                               !x.Deleted)
                    .ToListAsync();

                if (!performances.Any())
                {
                    return new DepartmentPerformanceAggregateDto
                    {
                        DepartmentId = departmentId,
                        RecordCount = 0
                    };
                }

                return new DepartmentPerformanceAggregateDto
                {
                    DepartmentId = departmentId,
                    AverageOverallScore = (double)performances.Average(p => p.OverallScore),
                    TotalScore = (double)performances.Sum(p => p.OverallScore),
                    RecordCount = performances.Count,
                    BestScore = (double)performances.Max(p => p.OverallScore),
                    WorstScore = (double)performances.Min(p => p.OverallScore),
                    BestPeriod = performances.OrderByDescending(p => p.OverallScore).First().Period,
                    WorstPeriod = performances.OrderBy(p => p.OverallScore).First().Period,
                    MetricAverages = new Dictionary<string, double>
                    {
                        ["AcademicStaffPerformance"] = (double)performances.Average(p => p.AcademicStaffPerformance),
                        ["ResearchPerformance"] = (double)performances.Average(p => p.ResearchPerformance),
                        ["PublicationPerformance"] = (double)performances.Average(p => p.PublicationPerformance),
                        ["StudentSatisfactionScore"] = (double)performances.Average(p => p.StudentSatisfactionScore),
                        ["InfrastructureScore"] = (double)performances.Average(p => p.InfrastructureScore),
                        ["BudgetEfficiencyScore"] = (double)performances.Average(p => p.BudgetEfficiencyScore),
                        ["CompletionRate"] = (double)performances.Average(p => p.CompletionRate)
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm performans verileri toplanırken hata - Department: {DepartmentId}", departmentId);
                throw;
            }
        }

        /// <summary>
        /// Fakülte geneli performans toplamını hesapla
        /// </summary>
        public async Task<FacultyPerformanceAggregateDto> AggregateFacultyPerformanceAsync(
            string facultyId,
            string period)
        {
            try
            {
                var performances = await GetByFacultyAsync(facultyId, period);

                if (!performances.Any())
                {
                    return new FacultyPerformanceAggregateDto
                    {
                        FacultyId = facultyId,
                        DepartmentCount = 0
                    };
                }

                return new FacultyPerformanceAggregateDto
                {
                    FacultyId = facultyId,
                    AverageOverallScore = (double)performances.Average(p => p.OverallScore),
                    DepartmentCount = performances.Count,
                    BestDepartmentScore = (double)performances.Max(p => p.OverallScore),
                    WorstDepartmentScore = (double)performances.Min(p => p.OverallScore),
                    BestDepartment = performances.OrderByDescending(p => p.OverallScore).First().DepartmentName ?? "",
                    WorstDepartment = performances.OrderBy(p => p.OverallScore).First().DepartmentName ?? "",
                    MetricAverages = new Dictionary<string, double>
                    {
                        ["AcademicStaffPerformance"] = (double)performances.Average(p => p.AcademicStaffPerformance),
                        ["ResearchPerformance"] = (double)performances.Average(p => p.ResearchPerformance),
                        ["PublicationPerformance"] = (double)performances.Average(p => p.PublicationPerformance),
                        ["StudentSatisfactionScore"] = (double)performances.Average(p => p.StudentSatisfactionScore),
                        ["InfrastructureScore"] = (double)performances.Average(p => p.InfrastructureScore),
                        ["BudgetEfficiencyScore"] = (double)performances.Average(p => p.BudgetEfficiencyScore),
                        ["CompletionRate"] = (double)performances.Average(p => p.CompletionRate)
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Fakülte performans verileri toplanırken hata - Faculty: {FacultyId}", facultyId);
                throw;
            }
        }

        #endregion

        #region Search Operations

        /// <summary>
        /// Bölüm performanslarında arama yap
        /// </summary>
        public async Task<PagedListDto<DepartmentPerformanceEntity>> SearchAsync(
            string searchTerm,
            PagedListCo<DepartmentPerformanceFilterCo> co)
        {
            try
            {
                var query = _context.DepartmentPerformances
                    .Where(x => !x.Deleted)
                    .AsQueryable();

                // Apply search
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    var lowerSearchTerm = searchTerm.ToLower();
                    query = query.Where(x =>
                        x.DepartmentName!.ToLower().Contains(lowerSearchTerm) ||
                        x.FacultyName!.ToLower().Contains(lowerSearchTerm) ||
                        x.Period.ToLower().Contains(lowerSearchTerm) ||
                        x.Status!.ToLower().Contains(lowerSearchTerm));
                }

                // Apply additional filters from co.Criteria if provided
                if (co.Criteria != null)
                {
                    // Apply the same filters as in GetPagedAsync
                    // (Implementation would be similar to GetPagedAsync method)
                }

                // Apply sorting and pagination
                query = query.OrderByDescending(x => x.EvaluationDate);

                var totalCount = await query.CountAsync();
                var items = await query
                    .Skip(co.Pager.Skip)
                    .Take(co.Pager.Size)
                    .ToListAsync();

                return new PagedListDto<DepartmentPerformanceEntity>
                {
                    Data = items,
                    Count = items.Count,
                    TotalCount = totalCount,
                    Page = co.Pager.Page,
                    Size = co.Pager.Size
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bölüm performanslarında arama yapılırken hata - SearchTerm: {SearchTerm}", searchTerm);
                throw;
            }
        }

        /// <summary>
        /// Gelişmiş filtreleme ile bölüm performanslarını getir
        /// </summary>
        public async Task<List<DepartmentPerformanceEntity>> GetWithAdvancedFiltersAsync(
            Dictionary<string, object> filters)
        {
            try
            {
                var query = _context.DepartmentPerformances
                    .Where(x => !x.Deleted)
                    .AsQueryable();

                foreach (var filter in filters)
                {
                    switch (filter.Key.ToLower())
                    {
                        case "departmentids":
                            if (filter.Value is List<string> departmentIds)
                            {
                                query = query.Where(x => departmentIds.Contains(x.DepartmentId));
                            }
                            break;
                        case "facultyids":
                            if (filter.Value is List<string> facultyIds)
                            {
                                query = query.Where(x => facultyIds.Contains(x.FacultyId));
                            }
                            break;
                        case "periods":
                            if (filter.Value is List<string> periods)
                            {
                                query = query.Where(x => periods.Contains(x.Period));
                            }
                            break;
                        case "minscore":
                            if (filter.Value is double minScore)
                            {
                                query = query.Where(x => x.OverallScore >= (decimal)minScore);
                            }
                            break;
                        case "maxscore":
                            if (filter.Value is double maxScore)
                            {
                                query = query.Where(x => x.OverallScore <= (decimal)maxScore);
                            }
                            break;
                        case "isapproved":
                            if (filter.Value is bool isApproved)
                            {
                                query = query.Where(x => x.IsApproved == isApproved);
                            }
                            break;
                    }
                }

                return await query.OrderByDescending(x => x.EvaluationDate).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Gelişmiş filtreleme ile bölüm performansları getirilirken hata");
                throw;
            }
        }

        #endregion

        #region Validation Operations

        /// <summary>
        /// Bölüm performans verilerinin tutarlılığını kontrol et
        /// </summary>
        public async Task<DataConsistencyReportDto> ValidateDataConsistencyAsync(
            string departmentId,
            string period)
        {
            try
            {
                var report = new DataConsistencyReportDto
                {
                    IsConsistent = true,
                    CheckedAt = DateTime.UtcNow
                };

                var performance = await GetByDepartmentAndPeriodAsync(departmentId, period);
                if (performance == null)
                {
                    report.Issues.Add($"Bölüm {departmentId} için {period} döneminde performans kaydı bulunamadı");
                    report.IsConsistent = false;
                    return report;
                }

                // Check score consistency
                if (performance.OverallScore < 0 || performance.OverallScore > 100)
                {
                    report.Issues.Add($"Genel skor geçersiz aralıkta: {performance.OverallScore}");
                    report.IsConsistent = false;
                }

                // Check individual scores
                var scores = new[]
                {
                    performance.AcademicStaffPerformance,
                    performance.ResearchPerformance,
                    performance.PublicationPerformance,
                    performance.StudentSatisfactionScore,
                    performance.InfrastructureScore,
                    performance.BudgetEfficiencyScore
                };

                foreach (var score in scores)
                {
                    if (score < 0 || score > 100)
                    {
                        report.Issues.Add($"Performans skoru geçersiz aralıkta: {score}");
                        report.IsConsistent = false;
                    }
                }

                // Check completion rate
                if (performance.CompletionRate < 0 || performance.CompletionRate > 100)
                {
                    report.Issues.Add($"Tamamlanma oranı geçersiz aralıkta: {performance.CompletionRate}");
                    report.IsConsistent = false;
                }

                // Check staff and student counts
                if (performance.TotalAcademicStaff < 0 || performance.TotalStudents < 0)
                {
                    report.Issues.Add("Personel veya öğrenci sayıları negatif olamaz");
                    report.IsConsistent = false;
                }

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Veri tutarlılığı kontrol edilirken hata - Department: {DepartmentId}", departmentId);
                throw;
            }
        }

        /// <summary>
        /// Duplicate kayıtları kontrol et
        /// </summary>
        public async Task<bool> CheckDuplicateRecordAsync(string departmentId, string period)
        {
            try
            {
                var count = await _context.DepartmentPerformances
                    .CountAsync(x => x.DepartmentId == departmentId &&
                                   x.Period == period &&
                                   !x.Deleted);

                return count > 1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Duplicate kayıt kontrolü yapılırken hata - Department: {DepartmentId}", departmentId);
                throw;
            }
        }

        #endregion

        #region Utility Operations

        /// <summary>
        /// Bölüm performans cache'ini temizle
        /// </summary>
        public async Task<bool> ClearCacheAsync(string departmentId)
        {
            try
            {
                // TODO: Implement cache clearing logic
                // This would typically involve clearing Redis cache or similar
                await Task.CompletedTask;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache temizlenirken hata - Department: {DepartmentId}", departmentId);
                return false;
            }
        }

        /// <summary>
        /// Performans verilerini bulk insert et - Enhanced with BulkOperationExtensions
        /// </summary>
        public async Task<BulkOperationResult> BulkInsertAsync(List<DepartmentPerformanceEntity> entities, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("DepartmentPerformanceStore bulk insert başlatılıyor - Entity Count: {Count}", entities.Count);

                // Apply business logic using extensions (temporarily disabled)
                // entities.ApplyPerformanceCalculations();
                // entities.ApplyAuditFields(DateTime.UtcNow, "system");

                // Validate entities before insert
                var validationErrors = ValidateEntitiesForInsert(entities);
                if (validationErrors.Any())
                {
                    _logger.LogWarning("Bulk insert validation hatası - Errors: {Errors}", string.Join(", ", validationErrors));
                    return new BulkOperationResult
                    {
                        Success = false,
                        ProcessedCount = 0,
                        ErrorMessage = $"Validation errors: {string.Join(", ", validationErrors)}"
                    };
                }

                // Use enhanced BulkOperationExtensions
                var result = await _context.BulkInsertAsync(
                    entities,
                    cancellationToken: cancellationToken);

                if (result.Success)
                {
                    _logger.LogInformation("DepartmentPerformanceStore bulk insert tamamlandı - Processed: {Count}, Time: {ElapsedTime}ms",
                        result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
                }
                else
                {
                    _logger.LogError("DepartmentPerformanceStore bulk insert başarısız - Error: {Error}", result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "DepartmentPerformanceStore bulk insert işlemi sırasında hata");
                return new BulkOperationResult
                {
                    Success = false,
                    ProcessedCount = 0,
                    ErrorMessage = ex.Message,
                    ElapsedTime = TimeSpan.Zero
                };
            }
        }

        /// <summary>
        /// Performans verilerini bulk update et - Enhanced with BulkOperationExtensions
        /// </summary>
        public async Task<BulkOperationResult> BulkUpdateAsync(List<DepartmentPerformanceEntity> entities, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("DepartmentPerformanceStore bulk update başlatılıyor - Entity Count: {Count}", entities.Count);

                // Apply business logic for updates (temporarily disabled)
                // entities.ApplyPerformanceCalculations();
                // entities.ApplyAuditFields(DateTime.UtcNow, "system");

                // Validate entities before update
                var validationErrors = ValidateEntitiesForUpdate(entities);
                if (validationErrors.Any())
                {
                    _logger.LogWarning("Bulk update validation hatası - Errors: {Errors}", string.Join(", ", validationErrors));
                    return new BulkOperationResult
                    {
                        Success = false,
                        ProcessedCount = 0,
                        ErrorMessage = $"Validation errors: {string.Join(", ", validationErrors)}"
                    };
                }

                // Use enhanced BulkOperationExtensions
                var result = await _context.BulkUpdateAsync(
                    entities,
                    cancellationToken: cancellationToken);

                if (result.Success)
                {
                    _logger.LogInformation("DepartmentPerformanceStore bulk update tamamlandı - Processed: {Count}, Time: {ElapsedTime}ms",
                        result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
                }
                else
                {
                    _logger.LogError("DepartmentPerformanceStore bulk update başarısız - Error: {Error}", result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "DepartmentPerformanceStore bulk update işlemi sırasında hata");
                return new BulkOperationResult
                {
                    Success = false,
                    ProcessedCount = 0,
                    ErrorMessage = ex.Message,
                    ElapsedTime = TimeSpan.Zero
                };
            }
        }

        #region Private Validation Methods

        /// <summary>
        /// Insert için entity validasyonu
        /// </summary>
        private List<string> ValidateEntitiesForInsert(List<DepartmentPerformanceEntity> entities)
        {
            var errors = new List<string>();

            foreach (var entity in entities)
            {
                if (string.IsNullOrEmpty(entity.DepartmentId))
                    errors.Add($"DepartmentId boş olamaz - Entity: {entity.Id}");

                if (string.IsNullOrEmpty(entity.Period))
                    errors.Add($"Period boş olamaz - Entity: {entity.Id}");

                if (entity.OverallScore < 0 || entity.OverallScore > 100)
                    errors.Add($"OverallScore 0-100 arasında olmalı - Entity: {entity.Id}, Score: {entity.OverallScore}");

                if (entity.EvaluationDate == default)
                    errors.Add($"EvaluationDate geçerli bir tarih olmalı - Entity: {entity.Id}");
            }

            return errors;
        }

        /// <summary>
        /// Update için entity validasyonu
        /// </summary>
        private List<string> ValidateEntitiesForUpdate(List<DepartmentPerformanceEntity> entities)
        {
            var errors = new List<string>();

            foreach (var entity in entities)
            {
                if (string.IsNullOrEmpty(entity.Id))
                    errors.Add("Update için entity Id boş olamaz");

                if (string.IsNullOrEmpty(entity.DepartmentId))
                    errors.Add($"DepartmentId boş olamaz - Entity: {entity.Id}");

                if (entity.OverallScore < 0 || entity.OverallScore > 100)
                    errors.Add($"OverallScore 0-100 arasında olmalı - Entity: {entity.Id}, Score: {entity.OverallScore}");
            }

            return errors;
        }

        /// <summary>
        /// Performans verilerini bulk delete et - Enhanced with BulkOperationExtensions
        /// </summary>
        public async Task<BulkOperationResult> BulkDeleteAsync(List<DepartmentPerformanceEntity> entities, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("DepartmentPerformanceStore bulk delete başlatılıyor - Entity Count: {Count}", entities.Count);

                // Validate entities before delete
                var validationErrors = ValidateEntitiesForDelete(entities);
                if (validationErrors.Any())
                {
                    _logger.LogWarning("Bulk delete validation hatası - Errors: {Errors}", string.Join(", ", validationErrors));
                    return new BulkOperationResult
                    {
                        Success = false,
                        ProcessedCount = 0,
                        ErrorMessage = $"Validation errors: {string.Join(", ", validationErrors)}"
                    };
                }

                // Use enhanced BulkOperationExtensions
                var result = await _context.BulkDeleteAsync(
                    entities,
                    cancellationToken: cancellationToken);

                if (result.Success)
                {
                    _logger.LogInformation("DepartmentPerformanceStore bulk delete tamamlandı - Processed: {Count}, Time: {ElapsedTime}ms",
                        result.ProcessedCount, result.ElapsedTime.TotalMilliseconds);
                }
                else
                {
                    _logger.LogError("DepartmentPerformanceStore bulk delete başarısız - Error: {Error}", result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "DepartmentPerformanceStore bulk delete işlemi sırasında hata");
                return new BulkOperationResult
                {
                    Success = false,
                    ProcessedCount = 0,
                    ErrorMessage = ex.Message,
                    ElapsedTime = TimeSpan.Zero
                };
            }
        }

        /// <summary>
        /// Delete için entity validasyonu
        /// </summary>
        private List<string> ValidateEntitiesForDelete(List<DepartmentPerformanceEntity> entities)
        {
            var errors = new List<string>();

            foreach (var entity in entities)
            {
                if (string.IsNullOrEmpty(entity.Id))
                    errors.Add("Delete için entity Id boş olamaz");

                if (string.IsNullOrEmpty(entity.DepartmentId))
                    errors.Add($"DepartmentId boş olamaz - Entity: {entity.Id}");
            }

            return errors;
        }

        #endregion

        #endregion
    }
}
