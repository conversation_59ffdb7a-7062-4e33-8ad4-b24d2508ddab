using AcademicPerformance.Extensions;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Services.Interfaces;
using Rlx.Shared.Models.Dtos;

namespace AcademicPerformance.Stores.Interfaces
{
    /// <summary>
    /// Portfolio kontrol store interface'i
    /// Ders bazlı portfolio verification işlemleri için veri erişimi
    /// </summary>
    public interface IPortfolioControlStore
    {
        #region Course Portfolio Verification Operations

        /// <summary>
        /// Yeni ders portfolio verification kaydı oluşturur
        /// </summary>
        /// <param name="entity">Oluşturulacak verification entity'si</param>
        /// <returns>Oluşturulan entity</returns>
        Task<CoursePortfolioVerificationEntity> CreateCourseVerificationAsync(CoursePortfolioVerificationEntity entity);

        /// <summary>
        /// Ders portfolio verification kaydını günceller
        /// </summary>
        /// <param name="entity">Güncellenecek verification entity'si</param>
        /// <returns>Güncellenen entity</returns>
        Task<CoursePortfolioVerificationEntity> UpdateCourseVerificationAsync(CoursePortfolioVerificationEntity entity);

        /// <summary>
        /// ID'ye göre ders portfolio verification kaydını getirir
        /// </summary>
        /// <param name="id">Verification ID'si</param>
        /// <returns>Verification entity'si</returns>
        Task<CoursePortfolioVerificationEntity?> GetCourseVerificationByIdAsync(string id);

        /// <summary>
        /// Akademisyen TC'sine göre ders verification kayıtlarını getirir
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC Kimlik Numarası</param>
        /// <param name="period">Dönem adı (opsiyonel)</param>
        /// <returns>Verification kayıtları listesi</returns>
        Task<IEnumerable<CoursePortfolioVerificationEntity>> GetCourseVerificationsByAcademicianTcAsync(
            string academicianTc, string? period = null);

        /// <summary>
        /// Belirli dönemdeki tüm ders verification kayıtlarını getirir
        /// </summary>
        /// <param name="period">Dönem adı</param>
        /// <returns>Verification kayıtları listesi</returns>
        Task<IEnumerable<CoursePortfolioVerificationEntity>> GetCourseVerificationsByPeriodAsync(string period);

        /// <summary>
        /// Bekleyen verification kayıtlarını getirir
        /// </summary>
        /// <param name="archivistId">Archivist ID (opsiyonel)</param>
        /// <param name="period">Dönem adı (opsiyonel)</param>
        /// <returns>Bekleyen verification kayıtları</returns>
        Task<IEnumerable<CoursePortfolioVerificationEntity>> GetPendingCourseVerificationsAsync(
            string? archivistId = null, string? period = null);

        /// <summary>
        /// Filtrelenmiş ders verification kayıtları arama
        /// </summary>
        /// <param name="request">Arama ve sayfalama parametreleri</param>
        /// <returns>Sayfalanmış verification kayıtları</returns>
        Task<PagedListDto<CoursePortfolioVerificationEntity>> SearchCourseVerificationsAsync(
            SearchCourseVerificationRequestDto request);

        /// <summary>
        /// Akademisyen, ders kodu ve dönem kombinasyonuna göre verification kaydını getirir
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC</param>
        /// <param name="courseCode">Ders kodu</param>
        /// <param name="period">Dönem adı</param>
        /// <returns>Verification entity'si</returns>
        Task<CoursePortfolioVerificationEntity?> GetCourseVerificationByKeyAsync(
            string academicianTc, string courseCode, string period);

        /// <summary>
        /// Course portfolio verifications bulk update - Enhanced with BulkOperationExtensions
        /// </summary>
        /// <param name="updates">Güncellenecek verification listesi</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Bulk operation result with metrics</returns>
        Task<BulkOperationResult> BulkUpdateCourseVerificationsAsync(IEnumerable<CoursePortfolioVerificationEntity> updates, CancellationToken cancellationToken = default);

        /// <summary>
        /// Course portfolio verifications bulk insert - Enhanced with BulkOperationExtensions
        /// </summary>
        /// <param name="entities">Entity listesi</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Bulk operation result with metrics</returns>
        Task<BulkOperationResult> BulkInsertAsync(List<CoursePortfolioVerificationEntity> entities, CancellationToken cancellationToken = default);

        /// <summary>
        /// Course portfolio verifications bulk delete - Enhanced with BulkOperationExtensions
        /// </summary>
        /// <param name="entities">Entity listesi</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Bulk operation result with metrics</returns>
        Task<BulkOperationResult> BulkDeleteAsync(List<CoursePortfolioVerificationEntity> entities, CancellationToken cancellationToken = default);

        /// <summary>
        /// Verification kaydını siler
        /// </summary>
        /// <param name="id">Verification ID'si</param>
        /// <returns>Silme işlemi başarılı mı</returns>
        Task<bool> DeleteCourseVerificationAsync(string id);

        #endregion

        #region Statistics and Reporting

        /// <summary>
        /// Akademisyen bazlı verification istatistiklerini getirir
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC</param>
        /// <param name="period">Dönem adı (opsiyonel)</param>
        /// <returns>İstatistik bilgileri</returns>
        Task<CourseVerificationStatisticsDto> GetAcademicianVerificationStatisticsAsync(
            string academicianTc, string? period = null);

        /// <summary>
        /// Dönem bazlı verification istatistiklerini getirir
        /// </summary>
        /// <param name="period">Dönem adı</param>
        /// <returns>İstatistik bilgileri</returns>
        Task<CourseVerificationStatisticsDto> GetPeriodVerificationStatisticsAsync(string period);

        /// <summary>
        /// Archivist bazlı verification istatistiklerini getirir
        /// </summary>
        /// <param name="archivistId">Archivist ID</param>
        /// <param name="period">Dönem adı (opsiyonel)</param>
        /// <returns>İstatistik bilgileri</returns>
        Task<CourseVerificationStatisticsDto> GetArchivistVerificationStatisticsAsync(
            string archivistId, string? period = null);

        /// <summary>
        /// Genel verification dashboard istatistiklerini getirir
        /// </summary>
        /// <param name="period">Dönem adı (opsiyonel)</param>
        /// <returns>Dashboard istatistikleri</returns>
        Task<VerificationDashboardStatisticsDto> GetDashboardStatisticsAsync(string? period = null);

        #endregion

        #region Sync Operations

        /// <summary>
        /// ArelBridge'den gelen ders bilgileri ile verification kayıtlarını senkronize eder
        /// </summary>
        /// <param name="courseInformations">ArelBridge'den gelen ders bilgileri</param>
        /// <returns>Senkronizasyon sonucu</returns>
        Task<SyncResultDto> SyncCourseVerificationsAsync(IEnumerable<CourseInformationDto> courseInformations);

        /// <summary>
        /// Belirli akademisyen için verification kayıtlarını senkronize eder
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC</param>
        /// <param name="courseInformations">Ders bilgileri</param>
        /// <returns>Senkronizasyon sonucu</returns>
        Task<SyncResultDto> SyncAcademicianCourseVerificationsAsync(
            string academicianTc, IEnumerable<CourseInformationDto> courseInformations);

        #endregion

        #region Health Check

        /// <summary>
        /// Store sağlık kontrolü yapar
        /// </summary>
        /// <returns>Sağlık durumu</returns>
        Task<(bool IsHealthy, string Message, Dictionary<string, object> Details)> HealthCheckAsync();

        #endregion
    }
}
