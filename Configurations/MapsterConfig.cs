using AcademicPerformance.Models.Cos;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.MongoDocuments;
using Mapster;

namespace AcademicPerformance.Configurations
{
    public static class MapsterConfig
    {
        public static void RegisterMappings()
        {

        }

        public static void Configure()
        {
            // UserContextCo to UserProfileDto mapping
            TypeAdapterConfig<UserContextCo, UserProfileDto>.NewConfig().TwoWays();

            // Submission Document to DTO mappings
            TypeAdapterConfig<AcademicSubmissionDocument, SubmissionDto>.NewConfig();
            TypeAdapterConfig<SubmissionDto, AcademicSubmissionDocument>.NewConfig();

            // Criterion Data mappings
            TypeAdapterConfig<SubmissionCriterionData, SubmissionCriterionDataDto>.NewConfig();
            TypeAdapterConfig<SubmissionCriterionDataDto, SubmissionCriterionData>.NewConfig();

            // Data Entry mappings
            TypeAdapterConfig<CriterionDataEntry, CriterionDataEntryDto>.NewConfig();
            TypeAdapterConfig<CriterionDataEntryDto, CriterionDataEntry>.NewConfig();

            // Feedback DTO mappings 
            // Bu mapping'ler feedback entity'leri oluşturulduktan sonra eklenecek

        }
    }
}
