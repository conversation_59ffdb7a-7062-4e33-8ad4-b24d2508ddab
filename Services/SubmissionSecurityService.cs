using AcademicPerformance.Interfaces;
using AcademicPerformance.Consts;
using AcademicPerformance.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace AcademicPerformance.Services
{
    /// <summary>
    /// Submission security service - Epic 4 Task 2.2
    /// Submission ownership validation ve authorization kontrolü
    /// </summary>
    public class SubmissionSecurityService : ISubmissionSecurityService
    {
        private readonly ISubmissionStore _submissionStore;
        private readonly IAcademicianStore _academicianStore;
        private readonly ILogger<SubmissionSecurityService> _logger;

        public SubmissionSecurityService(
            ISubmissionStore submissionStore,
            IAcademicianStore academicianStore,
            ILogger<SubmissionSecurityService> logger)
        {
            _submissionStore = submissionStore;
            _academicianStore = academicianStore;
            _logger = logger;
        }

        /// <summary>
        /// Kullanıcının submission'a erişim yetkisi var mı kontrol et
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="action"><PERSON><PERSON><PERSON><PERSON><PERSON> istenen action (read, write, approve, etc.)</param>
        /// <returns>Erişim yetkisi var mı?</returns>
        public async Task<bool> CanUserAccessSubmissionAsync(string userId, string submissionId, string action)
        {
            try
            {
                _logger.LogInformation($"Submission access kontrolü: User: {userId}, Submission: {submissionId}, Action: {action}");

                // Submission'ı getir
                var submission = await _submissionStore.GetSubmissionByIdAsync(submissionId);
                if (submission == null)
                {
                    _logger.LogWarning($"Submission bulunamadı: {submissionId}");
                    return false;
                }

                // Action'a göre kontrol et
                switch (action.ToLower())
                {
                    case "read":
                        return await CanReadSubmissionAsync(userId, submission);
                    case "write":
                    case "update":
                        return await CanWriteSubmissionAsync(userId, submission);
                    case "approve":
                    case "reject":
                        return await CanApproveSubmissionAsync(userId, submission);
                    case "delete":
                        return await CanDeleteSubmissionAsync(userId, submission);
                    default:
                        _logger.LogWarning($"Bilinmeyen action: {action}");
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Submission access kontrolü hatası: User: {userId}, Submission: {submissionId}, Action: {action}");
                return false;
            }
        }

        /// <summary>
        /// Controller'ın submission'a erişim yetkisi var mı kontrol et
        /// </summary>
        /// <param name="controllerId">Controller kullanıcı ID'si</param>
        /// <param name="submissionId">Submission ID'si</param>
        /// <returns>Erişim yetkisi var mı?</returns>
        public async Task<bool> IsControllerAuthorizedForSubmissionAsync(string controllerId, string submissionId)
        {
            try
            {
                _logger.LogInformation($"Controller authorization kontrolü: Controller: {controllerId}, Submission: {submissionId}");

                // Submission'ı getir
                var submission = await _submissionStore.GetSubmissionByIdAsync(submissionId);
                if (submission == null)
                {
                    _logger.LogWarning($"Submission bulunamadı: {submissionId}");
                    return false;
                }

                // Controller role kontrolü (şimdilik tüm controller'lar tüm submission'lara erişebilir)
                // Gelecekte role-based access control eklenecek

                // Status kontrolü - sadece Submitted veya UnderReview status'unda controller erişebilir
                var allowedStatuses = new[] { "Submitted", "UnderReview", "Approved", "Rejected" };
                if (!allowedStatuses.Contains(submission.Status))
                {
                    _logger.LogWarning($"Submission status controller erişimi için uygun değil: {submission.Status}");
                    return false;
                }

                _logger.LogInformation($"Controller authorization başarılı: Controller: {controllerId}, Submission: {submissionId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Controller authorization kontrolü hatası: Controller: {controllerId}, Submission: {submissionId}");
                return false;
            }
        }

        /// <summary>
        /// Submission ownership kontrolü
        /// </summary>
        /// <param name="userId">Kullanıcı ID'si</param>
        /// <param name="submissionId">Submission ID'si</param>
        /// <returns>Kullanıcı submission'ın sahibi mi?</returns>
        public async Task<bool> IsSubmissionOwnerAsync(string userId, string submissionId)
        {
            try
            {
                var submission = await _submissionStore.GetSubmissionByIdAsync(submissionId);
                if (submission == null)
                {
                    return false;
                }

                return submission.AcademicianUserId == userId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Submission ownership kontrolü hatası: User: {userId}, Submission: {submissionId}");
                return false;
            }
        }

        /// <summary>
        /// Submission status geçişinin geçerli olup olmadığını kontrol et
        /// </summary>
        /// <param name="currentStatus">Mevcut status</param>
        /// <param name="newStatus">Yeni status</param>
        /// <param name="userRole">Kullanıcı rolü</param>
        /// <returns>Geçiş geçerli mi?</returns>
        public Task<bool> IsValidStatusTransitionAsync(string currentStatus, string newStatus, string userRole)
        {
            try
            {
                // Valid transitions by role
                var validTransitions = new Dictionary<string, Dictionary<string, List<string>>>
                {
                    ["Academician"] = new Dictionary<string, List<string>>
                    {
                        { "Draft", new List<string> { "Submitted" } },
                        { "Rejected", new List<string> { "Draft", "Submitted" } }
                    },
                    ["Controller"] = new Dictionary<string, List<string>>
                    {
                        { "Submitted", new List<string> { "UnderReview", "Approved", "Rejected" } },
                        { "UnderReview", new List<string> { "Approved", "Rejected" } }
                    },
                    ["Admin"] = new Dictionary<string, List<string>>
                    {
                        { "Draft", new List<string> { "Submitted", "Cancelled" } },
                        { "Submitted", new List<string> { "UnderReview", "Approved", "Rejected", "Cancelled" } },
                        { "UnderReview", new List<string> { "Approved", "Rejected", "Cancelled" } },
                        { "Approved", new List<string> { "Cancelled" } },
                        { "Rejected", new List<string> { "Draft", "Cancelled" } }
                    }
                };

                if (!validTransitions.ContainsKey(userRole))
                {
                    _logger.LogWarning($"Bilinmeyen user role: {userRole}");
                    return Task.FromResult(false);
                }

                if (!validTransitions[userRole].ContainsKey(currentStatus))
                {
                    _logger.LogWarning($"Current status için geçiş tanımlanmamış: {currentStatus}, Role: {userRole}");
                    return Task.FromResult(false);
                }

                var isValid = validTransitions[userRole][currentStatus].Contains(newStatus);

                if (!isValid)
                {
                    _logger.LogWarning($"Geçersiz status geçişi: {currentStatus} -> {newStatus}, Role: {userRole}");
                }

                return Task.FromResult(isValid);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Status transition kontrolü hatası: {currentStatus} -> {newStatus}, Role: {userRole}");
                return Task.FromResult(false);
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Submission okuma yetkisi kontrolü
        /// </summary>
        private Task<bool> CanReadSubmissionAsync(string userId, dynamic submission)
        {
            // Owner her zaman okuyabilir
            if (submission.AcademicianUserId == userId)
            {
                return Task.FromResult(true);
            }

            // Controller'lar submitted submission'ları okuyabilir
            // TODO: Role kontrolü ekle
            var allowedStatuses = new[] { "Submitted", "UnderReview", "Approved", "Rejected" };
            return Task.FromResult(allowedStatuses.Contains((string)submission.Status));
        }

        /// <summary>
        /// Submission yazma yetkisi kontrolü
        /// </summary>
        private Task<bool> CanWriteSubmissionAsync(string userId, dynamic submission)
        {
            // Sadece owner yazabilir ve status Draft veya Rejected olmalı
            if (submission.AcademicianUserId != userId)
            {
                return Task.FromResult(false);
            }

            var allowedStatuses = new[] { "Draft", "Rejected" };
            return Task.FromResult(allowedStatuses.Contains((string)submission.Status));
        }

        /// <summary>
        /// Submission onaylama yetkisi kontrolü
        /// </summary>
        private Task<bool> CanApproveSubmissionAsync(string userId, dynamic submission)
        {
            // TODO: Controller role kontrolü ekle
            // Şimdilik status kontrolü yap
            var allowedStatuses = new[] { "Submitted", "UnderReview" };
            return Task.FromResult(allowedStatuses.Contains((string)submission.Status));
        }

        /// <summary>
        /// Submission silme yetkisi kontrolü
        /// </summary>
        private Task<bool> CanDeleteSubmissionAsync(string userId, dynamic submission)
        {
            // Sadece owner silebilir ve status Draft olmalı
            if (submission.AcademicianUserId != userId)
            {
                return Task.FromResult(false);
            }

            return Task.FromResult(submission.Status == "Draft");
        }

        #endregion
    }
}
