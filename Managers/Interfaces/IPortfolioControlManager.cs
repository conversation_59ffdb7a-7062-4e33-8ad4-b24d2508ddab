using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Stores.Interfaces;
using Rlx.Shared.Models.Dtos;

namespace AcademicPerformance.Managers.Interfaces
{
    /// <summary>
    /// Portfolio kontrol manager interface'i
    /// Business logic, validation ve ArelBridge entegrasyonu
    /// Portfolio kontrol modülü için kullanılır
    /// </summary>
    public interface IPortfolioControlManager
    {
        #region Course Portfolio Verification Operations

        /// <summary>
        /// Yeni ders portfolio verification kaydı oluşturur
        /// </summary>
        /// <param name="createDto">Oluşturulacak verification DTO'su</param>
        /// <param name="userId">İşlemi yapan kullanıcı ID'si</param>
        /// <returns>Oluşturulan verification DTO'su</returns>
        Task<CourseVerificationDto> CreateCourseVerificationAsync(CreateCourseVerificationDto createDto, string userId);

        /// <summary>
        /// Ders portfolio verification kaydını günceller
        /// </summary>
        /// <param name="updateDto">Güncellenecek verification DTO'su</param>
        /// <param name="userId">İşlemi yapan kullanıcı ID'si</param>
        /// <returns>Güncellenen verification DTO'su</returns>
        Task<CourseVerificationDto> UpdateCourseVerificationAsync(CourseVerificationUpdateDto updateDto, string userId);

        /// <summary>
        /// ID'ye göre ders portfolio verification kaydını getirir
        /// </summary>
        /// <param name="id">Verification ID'si</param>
        /// <returns>Verification DTO'su</returns>
        Task<CourseVerificationDto?> GetCourseVerificationByIdAsync(string id);

        /// <summary>
        /// Akademisyen TC'sine göre ders verification kayıtlarını getirir
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC Kimlik Numarası</param>
        /// <param name="period">Dönem adı (opsiyonel)</param>
        /// <returns>Verification DTO'ları listesi</returns>
        Task<IEnumerable<CourseVerificationDto>> GetCourseVerificationsByAcademicianTcAsync(
            string academicianTc, string? period = null);

        /// <summary>
        /// Akademisyen için ders verification kayıtlarını getirir (Controller için)
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC Kimlik Numarası</param>
        /// <returns>Verification DTO'ları listesi</returns>
        Task<IEnumerable<CourseVerificationDto>> GetAcademicianCourseVerificationsAsync(string academicianTc);

        /// <summary>
        /// Belirli dönemdeki tüm ders verification kayıtlarını getirir
        /// </summary>
        /// <param name="period">Dönem adı</param>
        /// <returns>Verification DTO'ları listesi</returns>
        Task<IEnumerable<CourseVerificationDto>> GetCourseVerificationsByPeriodAsync(string period);

        /// <summary>
        /// Bekleyen verification kayıtlarını getirir
        /// </summary>
        /// <param name="archivistId">Archivist ID (opsiyonel)</param>
        /// <param name="period">Dönem adı (opsiyonel)</param>
        /// <returns>Bekleyen verification DTO'ları</returns>
        Task<IEnumerable<CourseVerificationDto>> GetPendingCourseVerificationsAsync(
            string? archivistId = null, string? period = null);

        /// <summary>
        /// Filtrelenmiş ders verification kayıtları arama
        /// </summary>
        /// <param name="request">Arama ve sayfalama parametreleri</param>
        /// <returns>Sayfalanmış verification DTO'ları</returns>
        Task<PagedListDto<CourseVerificationDto>> SearchCourseVerificationsAsync(
            SearchCourseVerificationRequestDto request);

        /// <summary>
        /// Toplu verification güncelleme
        /// </summary>
        /// <param name="bulkUpdateDto">Toplu güncelleme DTO'su</param>
        /// <param name="userId">İşlemi yapan kullanıcı ID'si</param>
        /// <returns>Toplu güncelleme sonucu</returns>
        Task<BulkUpdateResultDto> BulkUpdateCourseVerificationsAsync(BulkCourseVerificationUpdateDto bulkUpdateDto, string userId);

        /// <summary>
        /// Verification kaydını siler
        /// </summary>
        /// <param name="id">Verification ID'si</param>
        /// <param name="userId">İşlemi yapan kullanıcı ID'si</param>
        /// <returns>Silme işlemi başarılı mı</returns>
        Task<bool> DeleteCourseVerificationAsync(string id, string userId);

        #endregion

        #region ArelBridge Integration

        /// <summary>
        /// ArelBridge'den ders bilgilerini çekerek verification kayıtlarını senkronize eder
        /// </summary>
        /// <param name="period">Dönem adı (opsiyonel)</param>
        /// <returns>Senkronizasyon sonucu</returns>
        Task<SyncResultDto> SyncCourseVerificationsFromArelBridgeAsync(string? period = null);

        /// <summary>
        /// Belirli akademisyen için ArelBridge'den ders bilgilerini çekerek senkronize eder
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC</param>
        /// <returns>Senkronizasyon sonucu</returns>
        Task<SyncResultDto> SyncAcademicianCourseVerificationsAsync(string academicianTc);

        /// <summary>
        /// ArelBridge bağlantısını test eder
        /// </summary>
        /// <returns>Bağlantı testi sonucu</returns>
        Task<(bool IsConnected, string Message)> TestArelBridgeConnectionAsync();

        #endregion

        #region Health Check

        /// <summary>
        /// Manager sağlık kontrolü yapar
        /// </summary>
        /// <returns>Sağlık durumu</returns>
        Task<(bool IsHealthy, string Message, Dictionary<string, object> Details)> HealthCheckAsync();

        #endregion

        #region Dashboard Operations

        /// <summary>
        /// Archivist dashboard verilerini getirir
        /// </summary>
        /// <param name="archivistId">Archivist ID</param>
        /// <param name="period">Dönem adı (opsiyonel)</param>
        /// <returns>Dashboard verileri</returns>
        Task<ArchivistDashboardResponseDto> GetArchivistDashboardAsync(string archivistId, string? period = null);

        /// <summary>
        /// Genel verification dashboard istatistiklerini getirir
        /// </summary>
        /// <param name="period">Dönem adı (opsiyonel)</param>
        /// <returns>Dashboard istatistikleri</returns>
        Task<VerificationDashboardStatisticsDto> GetDashboardStatisticsAsync(string? period = null);

        /// <summary>
        /// Akademisyen bazlı verification istatistiklerini getirir
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC</param>
        /// <param name="period">Dönem adı (opsiyonel)</param>
        /// <returns>İstatistik bilgileri</returns>
        Task<CourseVerificationStatisticsDto> GetAcademicianVerificationStatisticsAsync(
            string academicianTc, string? period = null);

        /// <summary>
        /// Dönem bazlı verification istatistiklerini getirir
        /// </summary>
        /// <param name="period">Dönem adı</param>
        /// <returns>İstatistik bilgileri</returns>
        Task<CourseVerificationStatisticsDto> GetPeriodVerificationStatisticsAsync(string period);

        /// <summary>
        /// Archivist bazlı verification istatistiklerini getirir
        /// </summary>
        /// <param name="archivistId">Archivist ID</param>
        /// <param name="period">Dönem adı (opsiyonel)</param>
        /// <returns>İstatistik bilgileri</returns>
        Task<CourseVerificationStatisticsDto> GetArchivistVerificationStatisticsAsync(
            string archivistId, string? period = null);

        #endregion

        #region Validation

        /// <summary>
        /// Verification güncelleme DTO'sunu validate eder
        /// </summary>
        /// <param name="updateDto">Güncelleme DTO'su</param>
        /// <returns>Validation sonucu</returns>
        Task<(bool IsValid, string ErrorMessage)> ValidateVerificationUpdateAsync(CourseVerificationUpdateDto updateDto);

        /// <summary>
        /// Verification oluşturma DTO'sunu validate eder
        /// </summary>
        /// <param name="createDto">Oluşturma DTO'su</param>
        /// <returns>Validation sonucu</returns>
        Task<(bool IsValid, string ErrorMessage)> ValidateVerificationCreateAsync(CreateCourseVerificationDto createDto);

        /// <summary>
        /// Akademisyen TC kimlik numarasını validate eder
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC</param>
        /// <returns>Validation sonucu</returns>
        Task<(bool IsValid, string ErrorMessage)> ValidateAcademicianTcAsync(string academicianTc);

        /// <summary>
        /// Verification status değerini validate eder
        /// </summary>
        /// <param name="status">Status değeri</param>
        /// <returns>Validation sonucu</returns>
        (bool IsValid, string ErrorMessage) ValidateVerificationStatus(string status);

        #endregion

        #region Health Check

        #endregion
    }
}
