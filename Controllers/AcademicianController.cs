using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using AcademicPerformance.Controllers.Base;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Models.Cos;
using AcademicPerformance.Consts;
using Rlx.Shared.Resources;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;
namespace AcademicPerformance.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class AcademicianController : BaseApiController
    {
        private readonly IAcademicianManager _academicianManager;
        private readonly ISubmissionManager _submissionManager;
        private readonly IUserContextHelper _userContextHelper;
        private readonly IRlxSystemLogHelper<AcademicianController> _systemLogHelper;
        private readonly ILogger<AcademicianController> _logger;
        public AcademicianController(
            IAcademicianManager academicianManager,
            ISubmissionManager submissionManager,
            IUserContextHelper userContextHelper,
            IRlxSystemLogHelper<AcademicianController> systemLogHelper,
            ILogger<AcademicianController> logger,
            IStringLocalizer<SharedResource> localizer) : base(localizer)
        {
            _academicianManager = academicianManager;
            _submissionManager = submissionManager;
            _userContextHelper = userContextHelper;
            _systemLogHelper = systemLogHelper;
            _logger = logger;
        }
        #region Dashboard Operations
        /// <summary>
        /// Akademisyenin dashboard'unu atanmış formlar ve istatistiklerle birlikte getir
        /// </summary>
        /// <returns>Giriş yapmış akademisyen için dashboard verileri</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.SubmitData)]
        public async Task<IActionResult> GetDashboard()
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                if (string.IsNullOrEmpty(userId))
                    return ErrorResponse(null, _localizer["UserNotFound"].Value);

                await _systemLogHelper.LogInfoAsync($"User {userId} requested dashboard");
                var dashboard = await _academicianManager.GetAcademicianDashboardAsync(userId);
                await _systemLogHelper.LogInfoAsync($"Successfully retrieved dashboard for user {userId}");
                return SuccessResponse(dashboard, _localizer["DashboardRetrievedSuccessfully"].Value);
            }
            catch (InvalidOperationException ex)
            {
                await _systemLogHelper.LogWarnAsync($"Dashboard access denied: {ex.Message}");
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving dashboard", ex);
                _logger.LogError(ex, "Error retrieving dashboard");
                return HandleException(ex, _localizer["ErrorRetrievingDashboard"].Value);
            }
        }
        /// <summary>
        /// Kimlik doğrulaması yapılmış akademisyen için atanmış formları sayfalanmış olarak getir
        /// </summary>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış atanmış form listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.SubmitData)]
        public async Task<IActionResult> GetForms([FromQuery] PagedListCo<GetAcademicianFormsCo> co)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                if (string.IsNullOrEmpty(userId))
                    return ErrorResponse(null, _localizer["UserNotFound"].Value);

                await _systemLogHelper.LogInfoAsync($"User {userId} requested assigned forms (page {co.Pager.Page}, size {co.Pager.Size})");
                var pagedAssignedForms = await _academicianManager.GetAssignedFormsAsync(userId, co);
                await _systemLogHelper.LogInfoAsync($"Retrieved {pagedAssignedForms.Count} assigned forms for user {userId} (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedAssignedForms, _localizer["AssignedFormsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving assigned forms", ex);
                _logger.LogError(ex, "Error retrieving assigned forms");
                return HandleException(ex, _localizer["ErrorRetrievingAssignedForms"].Value);
            }
        }
        /// <summary>
        /// Kimlik doğrulaması yapılmış akademisyen için dashboard istatistiklerini getir
        /// </summary>
        /// <returns>Dashboard istatistikleri</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ViewReports)]
        public async Task<IActionResult> GetStatistics()
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                if (string.IsNullOrEmpty(userId))
                    return ErrorResponse(null, _localizer["UserNotFound"].Value);

                await _systemLogHelper.LogInfoAsync($"User {userId} requested dashboard statistics");
                var statistics = await _academicianManager.GetDashboardStatisticsAsync(userId);
                await _systemLogHelper.LogInfoAsync($"Retrieved dashboard statistics for user {userId}");
                return SuccessResponse(statistics, _localizer["StatisticsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving dashboard statistics", ex);
                _logger.LogError(ex, "Error retrieving dashboard statistics");
                return HandleException(ex, _localizer["ErrorRetrievingStatistics"].Value);
            }
        }
        #endregion
        #region Profil Management
        /// <summary>
        /// Kimlik doğrulaması yapılmış kullanıcı için akademisyen profilini getir
        /// </summary>
        /// <returns>Akademisyen profil bilgileri</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.SubmitData)]
        public async Task<IActionResult> GetProfile()
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"User {userId} requested profile");
                var profile = await _academicianManager.GetAcademicianProfileAsync(userId);
                if (profile == null)
                {
                    return NotFoundResponse(_localizer["AcademicianProfileNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"Retrieved profile for user {userId}");
                return SuccessResponse(profile, _localizer["ProfileRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving profile", ex);
                _logger.LogError(ex, "Error retrieving profile");
                return HandleException(ex, _localizer["ErrorRetrievingProfile"].Value);
            }
        }
        /// <summary>
        /// Akademisyen profilini OrganizationManagement API ile senkronize et
        /// </summary>
        /// <param name="forceSync">Yakın zamanda senkronize edilmiş olsa bile zorla senkronize et</param>
        /// <returns>Senkronizasyon sonucu</returns>
        [HttpPost]
        [Authorize(APConsts.Policies.SubmitData)]
        public async Task<IActionResult> SyncProfile([FromQuery] bool forceSync = false)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"User {userId} requested profile sync (force: {forceSync})");
                var syncResult = await _academicianManager.SyncAcademicianProfileAsync(userId, forceSync);
                await _systemLogHelper.LogInfoAsync($"Profile sync completed for user {userId}: {syncResult.Action}");
                if (syncResult.Success)
                {
                    return SuccessResponse(syncResult, _localizer["ProfileSyncedSuccessfully"].Value);
                }
                else
                {
                    return BadRequestResponse(syncResult.ErrorMessage ?? _localizer["ProfileSyncFailed"].Value);
                }
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error syncing profile", ex);
                _logger.LogError(ex, "Error syncing profile");
                return HandleException(ex, _localizer["ErrorSyncingProfile"].Value);
            }
        }
        #endregion
        #region Submission Status Operations
        /// <summary>
        /// Belirli bir form için submission durumunu getir
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <returns>Submission durumu</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ViewData)]
        public async Task<IActionResult> GetSubmissionStatus(string formId)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                // Kullanıcı bu forma erişebilir mi kontrol et
                var canAccess = await _academicianManager.CanAcademicianAccessFormAsync(userId, formId);
                if (!canAccess)
                {
                    await _systemLogHelper.LogWarnAsync($"User {userId} attempted to access unauthorized form {formId}");
                    return Forbid();
                }
                await _systemLogHelper.LogInfoAsync($"User {userId} requested submission status for form {formId}");
                var submissionStatus = await _academicianManager.GetSubmissionStatusAsync(userId, formId);
                await _systemLogHelper.LogInfoAsync($"Retrieved submission status for user {userId}, form {formId}");
                return SuccessResponse(submissionStatus, _localizer["SubmissionStatusRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error retrieving submission status for form {formId}", ex);
                _logger.LogError(ex, "Error retrieving submission status for form {FormId}", formId);
                return HandleException(ex, _localizer["ErrorRetrievingSubmissionStatus"].Value);
            }
        }
        /// <summary>
        /// Akademisyenin tüm submission durumlarını sayfalanmış olarak getir
        /// </summary>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış submission durumları listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ViewSubmissions)]
        public async Task<IActionResult> GetSubmissions([FromQuery] PagedListCo<GetStatusFilterCo> co)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"User {userId} requested all submission statuses (page {co.Pager.Page}, size {co.Pager.Size})");
                var pagedSubmissionStatuses = await _academicianManager.GetAllSubmissionStatusesAsync(userId, co);
                await _systemLogHelper.LogInfoAsync($"Retrieved {pagedSubmissionStatuses.Count} submission statuses for user {userId} (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedSubmissionStatuses, _localizer["SubmissionStatusesRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving all submission statuses", ex);
                _logger.LogError(ex, "Error retrieving all submission statuses");
                return HandleException(ex, _localizer["ErrorRetrievingSubmissionStatuses"].Value);
            }
        }
        #endregion
        #region Quick Actions
        /// <summary>
        /// Kimlik doğrulaması yapılmış akademisyen için mevcut hızlı işlemleri sayfalanmış olarak getir
        /// </summary>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış hızlı işlem listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ViewData)]
        public async Task<IActionResult> GetQuickActions([FromQuery] PagedListCo<GetStatusFilterCo> co)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                // Model validation kontrolü
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }
                await _systemLogHelper.LogInfoAsync($"User {userId} requested quick actions (page {co.Pager.Page}, size {co.Pager.Size})");
                var pagedQuickActions = await _academicianManager.GetAvailableQuickActionsAsync(userId, co);
                await _systemLogHelper.LogInfoAsync($"Retrieved {pagedQuickActions.Count} quick actions for user {userId} (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedQuickActions, _localizer["QuickActionsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving quick actions", ex);
                _logger.LogError(ex, "Error retrieving quick actions");
                return HandleException(ex, _localizer["ErrorRetrievingQuickActions"].Value);
            }
        }
        /// <summary>
        /// Belirli bir form için hızlı işlem getir
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <returns>Form için hızlı işlem</returns>
        [HttpGet("forms/{formId}/quick-action")]
        [Authorize(APConsts.Policies.ViewData)]
        public async Task<IActionResult> GetQuickActionForForm(string formId)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                // Kullanıcının bu forma erişim yetkisi var mı kontrol et
                var canAccess = await _academicianManager.CanAcademicianAccessFormAsync(userId, formId);
                if (!canAccess)
                {
                    await _systemLogHelper.LogWarnAsync($"User {userId} attempted to access unauthorized form {formId}");
                    return Forbid();
                }
                await _systemLogHelper.LogInfoAsync($"User {userId} requested quick action for form {formId}");
                var quickAction = await _academicianManager.GetQuickActionForFormAsync(userId, formId);
                await _systemLogHelper.LogInfoAsync($"Retrieved quick action for user {userId}, form {formId}");
                return SuccessResponse(quickAction, _localizer["QuickActionRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error retrieving quick action for form {formId}", ex);
                _logger.LogError(ex, "Error retrieving quick action for form {FormId}", formId);
                return HandleException(ex, _localizer["ErrorRetrievingQuickAction"].Value);
            }
        }
        #endregion
        #region Analytics and Statistics
        /// <summary>
        /// Son teslim tarihi yaklaşan formları sayfalanmış olarak getir
        /// </summary>
        /// <param name="daysThreshold">Son teslim tarihi yaklaşma eşiği (varsayılan: 7 gün)</param>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış son teslim tarihi yaklaşan form listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ViewData)]
        public async Task<IActionResult> GetFormsWithApproachingDeadlines([FromQuery] int daysThreshold = 7, [FromQuery] PagedListCo<GetStatusFilterCo>? co = null)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                // Co null ise default değerler ata
                co ??= new PagedListCo<GetStatusFilterCo>();
                // Model validation kontrolü
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }
                if (co == null)
                    co = new PagedListCo<GetStatusFilterCo>();
                await _systemLogHelper.LogInfoAsync($"User {userId} requested forms with approaching deadlines (threshold: {daysThreshold} days, page {co.Pager.Page}, size {co.Pager.Size})");
                var pagedForms = await _academicianManager.GetFormsWithApproachingDeadlinesAsync(userId, daysThreshold, co);
                await _systemLogHelper.LogInfoAsync($"Retrieved {pagedForms.Count} forms with approaching deadlines for user {userId} (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedForms, _localizer["FormsWithApproachingDeadlinesRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving forms with approaching deadlines", ex);
                _logger.LogError(ex, "Error retrieving forms with approaching deadlines");
                return HandleException(ex, _localizer["ErrorRetrievingFormsWithApproachingDeadlines"].Value);
            }
        }
        /// <summary>
        /// Duruma göre submission istatistiklerini getir
        /// </summary>
        /// <returns>Duruma göre submission sayıları sözlüğü</returns>
        [HttpGet("submissions/statistics")]
        [Authorize(APConsts.Policies.ViewData)]
        public async Task<IActionResult> GetSubmissionStatistics()
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"User {userId} requested submission statistics");
                var statistics = await _academicianManager.GetSubmissionStatisticsByStatusAsync(userId);
                await _systemLogHelper.LogInfoAsync($"Retrieved submission statistics for user {userId}");
                return SuccessResponse(statistics, _localizer["SubmissionStatisticsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving submission statistics", ex);
                _logger.LogError(ex, "Error retrieving submission statistics");
                return HandleException(ex, _localizer["ErrorRetrievingSubmissionStatistics"].Value);
            }
        }
        /// <summary>
        /// Genel tamamlanma yüzdesini getir
        /// </summary>
        /// <returns>Genel tamamlanma yüzdesi</returns>
        [HttpGet("completion-percentage")]
        [Authorize(APConsts.Policies.ViewData)]
        public async Task<IActionResult> GetCompletionPercentage()
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"User {userId} requested completion percentage");
                var percentage = await _academicianManager.GetOverallCompletionPercentageAsync(userId);
                await _systemLogHelper.LogInfoAsync($"Retrieved completion percentage for user {userId}: {percentage}%");
                return SuccessResponse(new { completionPercentage = percentage }, _localizer["CompletionPercentageRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error retrieving completion percentage", ex);
                _logger.LogError(ex, "Error retrieving completion percentage");
                return HandleException(ex, _localizer["ErrorRetrievingCompletionPercentage"].Value);
            }
        }
        #endregion
        #region Validation Endpoints
        /// <summary>
        /// Akademisyenin belirli bir forma erişip erişemeyeceğini kontrol et
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <returns>Erişim izni sonucu</returns>
        [HttpGet("forms/{formId}/can-access")]
        [Authorize(APConsts.Policies.ViewData)]
        public async Task<IActionResult> CanAccessForm(string formId)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"User {userId} checking access to form {formId}");
                var canAccess = await _academicianManager.CanAcademicianAccessFormAsync(userId, formId);
                await _systemLogHelper.LogInfoAsync($"Access check for user {userId}, form {formId}: {canAccess}");
                return SuccessResponse(new { canAccess }, _localizer["AccessCheckCompletedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error checking access to form {formId}", ex);
                _logger.LogError(ex, "Error checking access to form {FormId}", formId);
                return HandleException(ex, _localizer["ErrorCheckingFormAccess"].Value);
            }
        }
        /// <summary>
        /// Akademisyenin belirli bir submission'ı düzenleyip düzenleyemeyeceğini kontrol et
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <returns>Düzenleme izni sonucu</returns>
        [HttpGet("forms/{formId}/can-edit")]
        [Authorize(APConsts.Policies.EditData)]
        public async Task<IActionResult> CanEditSubmission(string formId)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"User {userId} checking edit permission for form {formId}");
                var canEdit = await _academicianManager.CanAcademicianEditSubmissionAsync(userId, formId);
                await _systemLogHelper.LogInfoAsync($"Edit permission check for user {userId}, form {formId}: {canEdit}");
                return SuccessResponse(new { canEdit }, _localizer["EditPermissionCheckCompletedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error checking edit permission for form {formId}", ex);
                _logger.LogError(ex, "Error checking edit permission for form {FormId}", formId);
                return HandleException(ex, _localizer["ErrorCheckingEditPermission"].Value);
            }
        }
        /// <summary>
        /// Akademisyenin belirli bir formu gönderip gönderemeyeceğini kontrol et
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <returns>Gönderme izni sonucu</returns>
        [HttpGet("forms/{formId}/can-submit")]
        [Authorize(APConsts.Policies.SubmitData)]
        public async Task<IActionResult> CanSubmitForm(string formId)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                await _systemLogHelper.LogInfoAsync($"User {userId} checking submit permission for form {formId}");
                var canSubmit = await _academicianManager.CanAcademicianSubmitFormAsync(userId, formId);
                await _systemLogHelper.LogInfoAsync($"Submit permission check for user {userId}, form {formId}: {canSubmit}");
                return SuccessResponse(new { canSubmit }, _localizer["SubmitPermissionCheckCompletedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error checking submit permission for form {formId}", ex);
                _logger.LogError(ex, "Error checking submit permission for form {FormId}", formId);
                return HandleException(ex, _localizer["ErrorCheckingSubmitPermission"].Value);
            }
        }
        #endregion
        #region Submission Listesi ve Arama
        /// <summary>
        /// Akademisyenin submission'larını sayfalanmış olarak getir - Gelişmiş filtreleme ile
        /// </summary>
        [HttpPost("submissions/search")]
        [Authorize(APConsts.Policies.ViewReports)]
        public async Task<IActionResult> SearchSubmissions([FromBody] PagedListCo<GetAcademicSubmissionsCo> co)
        {
            try
            {
                var userId = GetTestUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return UnauthorizedResponse(_localizer["UserIdNotFoundInToken"].Value);
                }
                // Not: Bu endpoint gelecekte implement edilecek
                // Şu an için basit bir mock response döndürüyoruz
                var mockSubmissions = new PagedListDto<object>
                {
                    Count = 0,
                    Page = co.Pager.Page,
                    Size = co.Pager.Size,
                    Data = new List<object>()
                };
                await _systemLogHelper.LogInfoAsync($"User {userId} searched submissions (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(mockSubmissions, _localizer["SubmissionsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync("Error searching submissions", ex);
                _logger.LogError(ex, "Error searching submissions");
                return HandleException(ex, _localizer["InternalServerError"].Value);
            }
        }
        #endregion
        #region Submission Yönetimi
        /// <summary>
        /// Belirli bir form için akademisyenin submission'ını getir
        /// </summary>
        [HttpGet("forms/{formId}/submission")]
        [Authorize(APConsts.Policies.SubmitData)]
        public async Task<IActionResult> GetSubmission(string formId)
        {
            try
            {
                var userId = GetTestUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return UnauthorizedResponse(_localizer["UserIdNotFoundInToken"].Value);
                }
                var submission = await _submissionManager.GetSubmissionByFormIdAsync(userId, formId);
                if (submission == null)
                {
                    return NotFoundResponse(_localizer["SubmissionNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"User {userId} retrieved submission for form {formId}");
                return SuccessResponse(submission, _localizer["SubmissionRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error retrieving submission for form {formId}", ex);
                return HandleException(ex, _localizer["ErrorRetrievingSubmission"].Value);
            }
        }
        /// <summary>
        /// Bir form için yeni submission oluştur
        /// </summary>
        [HttpPost("{formId}")]
        [Authorize(APConsts.Policies.SubmitData)] // Veri gönderme yetkisi
        public async Task<IActionResult> CreateSubmission(string formId, [FromBody] SubmissionCreateDto dto)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                if (string.IsNullOrEmpty(userId))
                {
                    return UnauthorizedResponse(_localizer["UserIdNotFoundInToken"].Value);
                }
                // DTO'daki FormId'yi route'dan gelen ile eşitle
                dto.FormId = formId;
                var submission = await _submissionManager.CreateSubmissionAsync(userId, dto);
                await _systemLogHelper.LogInfoAsync($"User {userId} created submission for form {formId}");
                return SuccessResponse(submission, _localizer["SubmissionCreatedSuccessfully"].Value);
            }
            catch (UnauthorizedAccessException)
            {
                return BadRequestResponse(_localizer["FormNotFoundOrAccessDenied"].Value);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequestResponse(ex.Message);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error creating submission for form {formId}", ex);
                return HandleException(ex, _localizer["ErrorCreatingSubmission"].Value);
            }
        }
        /// <summary>
        /// Submission'ı güncelle (taslak kaydetme)
        /// </summary>
        [HttpPut("forms/{formId}/submission")]
        [Authorize(APConsts.Policies.EditData)]
        public async Task<IActionResult> UpdateSubmission(string formId, [FromBody] SubmissionUpdateDto dto)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                var success = await _submissionManager.UpdateSubmissionAsync(userId, dto);
                if (!success)
                {
                    return NotFoundResponse(_localizer["SubmissionNotFoundOrCannotUpdate"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"User {userId} updated submission {dto.Id}");
                return SuccessResponse<object>(null, _localizer["SubmissionUpdatedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error updating submission {dto.Id}", ex);
                return HandleException(ex, _localizer["ErrorUpdatingSubmission"].Value);
            }
        }
        /// <summary>
        /// Belirli bir kriter için performans verisi gir
        /// </summary>
        [HttpPost]
        [Authorize(APConsts.Policies.SubmitData)]
        public async Task<IActionResult> InputData(string formId, string criterionLinkId, [FromBody] CriterionDataInputDto dto)
        {
            try
            {
                var userId = GetTestUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return UnauthorizedResponse(_localizer["UserIdNotFoundInToken"].Value);
                }
                var success = await _submissionManager.InputCriterionDataAsync(userId, formId, criterionLinkId, dto);
                if (!success)
                {
                    return BadRequestResponse(_localizer["ErrorInputtingCriterionData"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"User {userId} input data for criterion {criterionLinkId} in form {formId}");
                return SuccessResponse<object>(null, _localizer["CriterionDataInputSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error inputting criterion data for {criterionLinkId}", ex);
                return HandleException(ex, _localizer["ErrorInputtingCriterionData"].Value);
            }
        }
        /// <summary>
        /// Belirli bir kriter veri girişini güncelle
        /// </summary>
        [HttpPut]
        [Authorize(APConsts.Policies.EditData)]
        public async Task<IActionResult> UpdateData(string formId, string criterionLinkId, string dataId, [FromBody] CriterionDataUpdateDto dto)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                var success = await _submissionManager.UpdateCriterionDataAsync(userId, formId, criterionLinkId, dataId, dto);
                if (!success)
                {
                    return NotFoundResponse(_localizer["CriterionDataNotFoundOrCannotUpdate"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"User {userId} updated criterion data {dataId} for criterion {criterionLinkId}");
                return SuccessResponse<object>(null, _localizer["CriterionDataUpdatedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error updating criterion data {dataId}", ex);
                return HandleException(ex, _localizer["ErrorUpdatingCriterionData"].Value);
            }
        }
        /// <summary>
        /// Belirli bir kriter veri girişini sil
        /// </summary>
        [HttpDelete]
        [Authorize(APConsts.Policies.EditData)]
        public async Task<IActionResult> DeleteData(string formId, string criterionLinkId, string dataId)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                var success = await _submissionManager.DeleteCriterionDataAsync(userId, formId, criterionLinkId, dataId);
                if (!success)
                {
                    return NotFoundResponse(_localizer["CriterionDataNotFound"].Value);
                }
                await _systemLogHelper.LogInfoAsync($"User {userId} deleted criterion data {dataId} for criterion {criterionLinkId}");
                return SuccessResponse<object>(null, _localizer["CriterionDataDeletedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error deleting criterion data {dataId}", ex);
                return HandleException(ex, _localizer["ErrorDeletingCriterionData"].Value);
            }
        }
        /// <summary>
        /// Belirli bir kriter için kriter veri girişlerini sayfalanmış olarak getir
        /// </summary>
        /// <param name="formId">Form ID'si</param>
        /// <param name="criterionLinkId">Kriter bağlantı ID'si</param>
        /// <param name="co">Pagination ve filtreleme parametreleri</param>
        /// <returns>Sayfalanmış kriter veri girişleri listesi</returns>
        [HttpGet]
        [Authorize(APConsts.Policies.ViewData)]
        public async Task<IActionResult> GetCriterionDataEntries(string formId, string criterionLinkId, [FromQuery] PagedListCo<GetStatusFilterCo> co)
        {
            try
            {
                var userId = _userContextHelper.GetUserId()!;
                // Model validation kontrolü
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }
                var pagedDataEntries = await _submissionManager.GetCriterionDataEntriesAsync(userId, formId, criterionLinkId, co);
                await _systemLogHelper.LogInfoAsync($"User {userId} retrieved {pagedDataEntries.Count} criterion data entries for criterion {criterionLinkId} (page {co.Pager.Page}, size {co.Pager.Size})");
                return SuccessResponse(pagedDataEntries, _localizer["CriterionDataEntriesRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                await _systemLogHelper.LogErrorAsync($"Error retrieving criterion data entries for {criterionLinkId}", ex);
                return HandleException(ex, _localizer["ErrorRetrievingCriterionDataEntries"].Value);
            }
        }
        #endregion
        #region Test İçin Yardımcı Metotlar
        private string GetTestUserId()
        {
            // Gerçek implementasyon kullan
            return _userContextHelper.GetUserId()! ?? "test_user_123";
        }
        #endregion

        /// </summary>
        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="criterionLinkId">Criterion Link ID'si</param>
        /// <param name="file">Yüklenecek dosya</param>
        /// <param name="description">Dosya açıklaması (opsiyonel)</param>
        /// <returns>Upload sonucu ve criterion data güncellemesi</returns>
        [HttpPost("submissions/{submissionId}/criteria/{criterionLinkId}/upload")]
        [Authorize(APConsts.Policies.SubmitData)]
        public async Task<IActionResult> UploadCriterionEvidence(
            string submissionId,
            string criterionLinkId,
            IFormFile file,
            [FromForm] string? description = null)
        {
            try
            {
                var userId = GetTestUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return UnauthorizedResponse(_localizer["UserIdNotFoundInToken"].Value);
                }

                await _systemLogHelper.LogInfoAsync($"Integrated file upload başlatıldı: User: {userId}, Submission: {submissionId}, Criterion: {criterionLinkId}");

                // Submission ownership kontrolü
                var submission = await _submissionManager.GetSubmissionByIdAsync(userId, submissionId);
                if (submission == null)
                {
                    return NotFoundResponse(_localizer["SubmissionNotFound"].Value);
                }

                // File validation
                if (file == null || file.Length == 0)
                {
                    return BadRequestResponse(_localizer["FileRequired"].Value);
                }

                // Criterion data input DTO'su oluştur
                var criterionDataDto = new CriterionDataInputDto
                {
                    DataEntries = new List<CriterionDataEntryDto>
                    {
                        new CriterionDataEntryDto
                        {
                            FieldName = "EvidenceFile",
                            FieldType = "File",
                            Value = file.FileName,
                            Description = description ?? $"Evidence file: {file.FileName}",
                            CreatedAt = DateTime.UtcNow
                        }
                    },
                    Notes = $"File uploaded: {file.FileName} ({file.Length} bytes)"
                };

                // Önce criterion data'yı kaydet
                var criterionSuccess = await _submissionManager.InputCriterionDataAsync(userId, submission.FormId, criterionLinkId, criterionDataDto);
                if (!criterionSuccess)
                {
                    return BadRequestResponse(_localizer["ErrorInputtingCriterionData"].Value);
                }

                // TODO: File upload işlemi - şimdilik mock response
                // Gerçek implementasyonda FileUploadController'daki logic kullanılacak
                var mockFileResult = new
                {
                    fileId = Guid.NewGuid().ToString(),
                    fileName = file.FileName,
                    size = file.Length,
                    contentType = file.ContentType,
                    uploadedAt = DateTime.UtcNow
                };

                await _systemLogHelper.LogInfoAsync($"Integrated file upload başarılı: User: {userId}, Submission: {submissionId}, FileId: {mockFileResult.fileId}");

                return SuccessResponse(new
                {
                    submission = new { id = submissionId },
                    criterion = new { linkId = criterionLinkId },
                    file = mockFileResult,
                    message = "File uploaded and linked to criterion successfully"
                }, _localizer["FileUploadedSuccessfully"].Value);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized file upload attempt: User: {UserId}, Submission: {SubmissionId}", GetTestUserId(), submissionId);
                return UnauthorizedResponse(_localizer["UnauthorizedSubmissionAccess"].Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Integrated file upload hatası: User: {UserId}, Submission: {SubmissionId}", GetTestUserId(), submissionId);
                return HandleException(ex, _localizer["ErrorUploadingFile"].Value);
            }
        }

        /// <param name="submissionId">Submission ID'si</param>
        /// <param name="files">Yüklenecek dosyalar</param>
        /// <param name="criterionMappings">Dosya-kriter eşleştirmeleri (JSON format)</param>
        /// <returns>Bulk upload sonuçları</returns>
        [HttpPost("submissions/{submissionId}/bulk-upload")]
        [Authorize(APConsts.Policies.SubmitData)]
        public async Task<IActionResult> BulkUploadCriterionEvidence(
            string submissionId,
            List<IFormFile> files,
            [FromForm] string criterionMappings)
        {
            try
            {
                var userId = GetTestUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return UnauthorizedResponse(_localizer["UserIdNotFoundInToken"].Value);
                }

                await _systemLogHelper.LogInfoAsync($"Bulk file upload başlatıldı: User: {userId}, Submission: {submissionId}, FileCount: {files?.Count ?? 0}");

                // Submission ownership kontrolü
                var submission = await _submissionManager.GetSubmissionByIdAsync(userId, submissionId);
                if (submission == null)
                {
                    return NotFoundResponse(_localizer["SubmissionNotFound"].Value);
                }

                // Files validation
                if (files == null || !files.Any())
                {
                    return BadRequestResponse(_localizer["FilesRequired"].Value);
                }

                // TODO: Criterion mappings parse et ve bulk upload işlemi yap
                // Şimdilik mock response
                var uploadResults = files.Select((file, index) => new
                {
                    fileId = Guid.NewGuid().ToString(),
                    fileName = file.FileName,
                    size = file.Length,
                    contentType = file.ContentType,
                    uploadedAt = DateTime.UtcNow,
                    criterionLinkId = $"criterion-{index + 1}", // Mock criterion mapping
                    success = true
                }).ToList();

                await _systemLogHelper.LogInfoAsync($"Bulk file upload başarılı: User: {userId}, Submission: {submissionId}, UploadedCount: {uploadResults.Count}");

                return SuccessResponse(new
                {
                    submission = new { id = submissionId },
                    uploadResults = uploadResults,
                    totalFiles = files.Count,
                    successfulUploads = uploadResults.Count(r => r.success)
                }, _localizer["BulkFileUploadSuccessful"].Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Bulk file upload hatası: User: {UserId}, Submission: {SubmissionId}", GetTestUserId(), submissionId);
                return HandleException(ex, _localizer["ErrorUploadingFiles"].Value);
            }
        }
    }
}
